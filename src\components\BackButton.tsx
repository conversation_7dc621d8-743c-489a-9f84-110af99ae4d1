// Button to navigate back to the previous page.
import React from 'react'
import { useNavigate } from 'react-router-dom'
import { ArrowLeft } from 'lucide-react'

interface BackButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  className?: string
}

// Combines CSS class names, filtering out falsy values
const cn = (...classes: (string | undefined)[]) => {
  return classes.filter(Boolean).join(' ')
}

export const BackButton: React.FC<BackButtonProps> = ({ className, ...props }) => {
  const navigate = useNavigate()

  // Navigate back one step in history
  const handleBack = () => {
    navigate(-1)
  }

  return (
    <button
      onClick={handleBack}
      className={cn(
        'flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium transition-colors',
        'hover:bg-accent hover:text-accent-foreground',
        'text-muted-foreground',
        className
      )}
      {...props}
    >
      <ArrowLeft className="h-4 w-4" />
      Back
    </button>
  )
} 