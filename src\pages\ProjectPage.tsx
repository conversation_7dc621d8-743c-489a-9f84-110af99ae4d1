// Displays details for a specific project and its courthouses.
import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { CourthouseList } from '../components/CourthouseList';
import { getCourthouses, getProject } from '../lib/db';
import type { Database } from '../types/supabase';
import { LoadingAnimation } from '../components/LoadingAnimation';
import { usePageLoading } from '../hooks/usePageLoading';
import { useUser } from '@/contexts/UserContext';

type Courthouse = Database['public']['Tables']['courthouses']['Row'];
type Project = Database['public']['Tables']['projects']['Row'];

export function ProjectPage() {
  const { projectId } = useParams();
  const navigate = useNavigate();
  const { user } = useUser();
  const [project, setProject] = useState<Project | null>(null);
  const [courthouses, setCourthouses] = useState<Courthouse[]>([]);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const { loading, fadeOut } = usePageLoading(isInitialLoading);
  const [error, setError] = useState<string | null>(null);
  const [accessDenied, setAccessDenied] = useState(false);

  const loadData = async () => {
    if (!projectId) return;

    try {
      // First get the project data to check if it's CMP
      const projectData = await getProject(projectId);

      // Check if JUDCO user is trying to access CMP project
      if (user?.company === 'JUDCO' && projectData?.name.includes('CMP')) {
        console.log('JUDCO user attempting to access CMP project');
        setAccessDenied(true);
        setIsInitialLoading(false);
        return;
      }

      // If access is allowed, get the courthouses
      const courthousesData = await getCourthouses(projectId);

      setProject(projectData);
      setCourthouses(courthousesData);
    } catch (err) {
      console.error('Error loading project data:', err);
      setError('Failed to load project data. Please try again later.');
    } finally {
      setIsInitialLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [projectId, user]);

  if (loading) {
    return <LoadingAnimation fadeOut={fadeOut} />;
  }

  if (accessDenied) {
    // Redirect to dashboard after a short delay
    useEffect(() => {
      const timer = setTimeout(() => {
        navigate('/');
      }, 3000);
      return () => clearTimeout(timer);
    }, [navigate]);

    return (
      <div className="flex-1 p-6">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <h3 className="text-lg font-semibold">Access Denied</h3>
          <p>You do not have permission to view this project.</p>
          <p className="mt-2">Redirecting to dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 p-6">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="flex-1 p-6">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
          Project not found
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 p-6">
      <div className="mb-8 text-center">
        <h2 className="text-2xl font-bold text-gray-800">{project.name} Courthouses</h2>
        <p className="text-gray-600">View and manage courthouses in this project</p>
      </div>

      {courthouses.length > 0 ? (
        <CourthouseList
          courthouses={courthouses}
          projectId={projectId || ''}
          onCourthouseCreated={loadData}
        />
      ) : (
        <div className="text-center text-gray-500">
          No courthouses found in this project.
        </div>
      )}
    </div>
  );
}