import { useState, useEffect } from 'react';

export const usePageLoading = (isLoading: boolean, minDuration = 500) => {
  const [loading, setLoading] = useState(true);
  const [fadeOut, setFadeOut] = useState(false);

  useEffect(() => {
    if (!isLoading) {
      const startTime = Date.now();
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, minDuration - elapsedTime);

      // Start fade out animation
      setTimeout(() => {
        setFadeOut(true);
      }, remainingTime - 100); // Start fade out 100ms before finishing

      // Set loading to false after minimum duration
      setTimeout(() => {
        setLoading(false);
        setFadeOut(false);
      }, remainingTime);
    } else {
      setLoading(true);
      setFadeOut(false);
    }
  }, [isLoading, minDuration]);

  return { loading, fadeOut };
}; 