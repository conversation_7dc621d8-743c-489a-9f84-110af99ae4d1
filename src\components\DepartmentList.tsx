import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronRight, Search, CheckCircle2, AlertCircle, Grid, Flag, Plus, AlertTriangle } from 'lucide-react';
import { Department, ChecklistType } from '../types';
import { cn } from '../lib/utils';
import { Button } from '@/components/ui/button';
import { format } from 'date-fns';
import { CreateDepartmentDialog } from './CreateDepartmentDialog';
import { supabase } from '../lib/supabase';
import { useUser } from '@/contexts/UserContext';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface DepartmentListProps {
  departments: Department[];
  courthouseId: string;
  courthouseName: string;
  onDepartmentCreated?: () => void;
}

// Add IssueStatusBadge component
const IssueStatusBadge = ({ status }: { status: 'Open' | 'In Progress' | 'Resolved' }) => {
  const colors = {
    'Open': 'bg-blue-100 text-blue-800 border-blue-200',
    'In Progress': 'bg-purple-100 text-purple-800 border-purple-200',
    'Resolved': 'bg-gray-100 text-gray-800 border-gray-200',
  };

  return (
    <span className={cn(
      'px-2 py-1 text-xs font-medium rounded-full border',
      colors[status]
    )}>
      {status}
    </span>
  );
};

// Formats checklist type strings (e.g., 'rack-preparation') for display.
const formatChecklistType = (type: string): string => {
  // Handle known checklist types explicitly if needed
  if (type === 'uat') return 'UAT';
  if (type === 'cvcs') return 'CVCS';
  if (type === 'advanced-staging') return 'Advanced Staging';
  if (type === 'rack-preparation') return 'Rack Preparation';
  if (type === 'engineering') return 'Engineering';
  // Format custom/other types: replace dashes, capitalize words
  return type
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

export function DepartmentList({ departments, courthouseId, courthouseName, onDepartmentCreated }: DepartmentListProps) {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [filter, setFilter] = useState<'all' | 'incomplete' | 'in-progress' | 'complete' | 'open-issues'>('all');
  const { user } = useUser();

  React.useEffect(() => {
    // Subscribe to real-time updates
    const channel = supabase
      .channel('department-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'departments',
          filter: `courthouse_id=eq.${courthouseId}`,
        },
        () => {
          // Refresh the list when changes occur
          onDepartmentCreated?.();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [courthouseId, onDepartmentCreated]);

  const getProgressColor = (progress: number): string => {
    if (progress === 100) return 'bg-green-500';
    if (progress >= 75) return 'bg-yellow-500';
    if (progress >= 40) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const filteredDepartments = departments.filter(department => {
    const matchesSearch = department.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filter === 'all' ? true :
      filter === 'complete' ? department.progress >= 95 :
      filter === 'incomplete' ? department.progress === 0 :
      filter === 'open-issues' ? department.issues?.some(issue => issue.status === 'Open') :
      // In Progress: progress is between 0 and 95
      department.progress > 0 && department.progress < 95;
    
    return matchesSearch && matchesFilter;
  });

  // Calculate counts for each filter, including open issues
  const counts = {
    all: departments.length,
    incomplete: departments.filter(d => d.progress === 0).length,
    inProgress: departments.filter(d => d.progress > 0 && d.progress < 95).length,
    complete: departments.filter(d => d.progress >= 95).length,
    openIssues: departments.filter(d => d.issues?.some(issue => issue.status === 'Open')).length
  };

  const getChecklistProgress = (department: Department, type: ChecklistType) => {
    const checklist = department.checklists?.find(c => c.type === type);
    if (!checklist?.checklist_items?.length) return 0;

    // Filter out header items before calculating progress (similar to DepartmentDetailsPage)
    const taskItems = checklist.checklist_items.filter(item => !(item as any).is_header);
    if (taskItems.length === 0) return 0;

    const completed = taskItems.filter(item => item.completed).length;
    return Math.round((completed / taskItems.length) * 100);
  };

  const handleBirdsEyeView = () => {
    const departmentsWithFloor = departments.map(dept => {
      // Calculate overall progress based on all available checklists
      const enabledChecklists = [];

      // Check for all possible checklist types that exist in the department
      if (dept.checklists && dept.checklists.length > 0) {
        dept.checklists.forEach(checklist => {
          const progress = getChecklistProgress(dept, checklist.type);
          enabledChecklists.push(progress);
        });
      }

      // Fallback: if no checklists found, check boolean flags and calculate manually
      if (enabledChecklists.length === 0) {
        if (dept.has_rack_preparation_checklist) enabledChecklists.push(getChecklistProgress(dept, 'rack-preparation'));
        if (dept.has_engineering_checklist) enabledChecklists.push(getChecklistProgress(dept, 'engineering'));
        if (dept.has_technicians_checklist) enabledChecklists.push(getChecklistProgress(dept, 'technicians'));
        if (dept.has_uat_checklist) enabledChecklists.push(getChecklistProgress(dept, 'uat'));
        if (dept.has_cvcs_checklist) enabledChecklists.push(getChecklistProgress(dept, 'cvcs'));
        if (dept.has_advanced_staging_checklist) enabledChecklists.push(getChecklistProgress(dept, 'advanced-staging'));
      }

      const overallProgress = enabledChecklists.length > 0
        ? Math.round(enabledChecklists.reduce((sum, progress) => sum + progress, 0) / enabledChecklists.length)
        : dept.progress || 0; // Use stored progress as fallback

      return {
        id: dept.id,
        name: dept.name,
        progress: overallProgress,
        floor: dept.floor || 1, // Use floor from database, default to 1
        reference_number: dept.reference_number // Pass reference number along
      };
    });

    navigate('/departments/birds-eye', {
      state: {
        departments: departmentsWithFloor,
        courthouseName
      }
    });
  };

  const handleIssueLog = () => {
    navigate('/departments/issues', { 
      state: { 
        courthouseName,
        courthouseId: departments[0]?.courthouse_id
      } 
    });
  };

  return (
    <div className="w-full max-w-5xl mx-auto bg-white/30 backdrop-blur-lg rounded-xl shadow-lg border border-gray-200">
      {/* Search and filters header - sticky */}
      <div className="sticky top-0 z-10 bg-white/80 backdrop-blur-sm p-4 border-b border-gray-200 rounded-t-xl">
        <div className="space-y-3">
          {/* Search and Create button */}
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search departments..."
                className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white/50"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            {user?.company === 'admin' && (
              <CreateDepartmentDialog 
                courthouseId={courthouseId}
                onDepartmentCreated={onDepartmentCreated}
              />
            )}
          </div>

          {/* Filter buttons */}
          <div className="flex flex-col sm:flex-row gap-2 text-sm">
            <div className="grid grid-cols-2 sm:flex sm:flex-row gap-2 sm:flex-1">
              <Button
                variant={filter === 'all' ? 'default' : 'outline'}
                onClick={() => setFilter('all')}
                className="text-sm w-full sm:w-auto"
              >
                All ({counts.all})
              </Button>
              <Button
                variant={filter === 'open-issues' ? 'default' : 'outline'}
                onClick={() => setFilter('open-issues')}
                className="text-sm w-full sm:w-auto"
              >
                Issues ({counts.openIssues})
              </Button>
              <Button
                variant={filter === 'incomplete' ? 'default' : 'outline'}
                onClick={() => setFilter('incomplete')}
                className="text-sm w-full sm:w-auto"
              >
                Not Started ({counts.incomplete})
              </Button>
              <Button
                variant={filter === 'in-progress' ? 'default' : 'outline'}
                onClick={() => setFilter('in-progress')}
                className="text-sm w-full sm:w-auto"
              >
                In Progress ({counts.inProgress})
              </Button>
              <Button
                variant={filter === 'complete' ? 'default' : 'outline'}
                onClick={() => setFilter('complete')}
                className="text-sm w-full sm:w-auto"
              >
                Complete ({counts.complete})
              </Button>
            </div>
            
            <div className="flex gap-2 w-full sm:w-auto sm:ml-auto">
              {/* Issue Log Button */}
              <Button
                variant="outline"
                onClick={handleIssueLog}
                className="w-full sm:w-auto flex items-center justify-center gap-2"
              >
                <Flag className="h-4 w-4" />
                <span>Issue Log</span>
              </Button>
              
              {/* Birds Eye View Button */}
              <Button
                variant="outline"
                onClick={handleBirdsEyeView}
                className="w-full sm:w-auto flex items-center justify-center gap-2"
              >
                <Grid className="h-4 w-4" />
                <span>Birds Eye View</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Scrollable list container */}
      <div className="h-[calc(100vh-26rem)] overflow-y-auto">
        <div className="divide-y divide-gray-100">
          {filteredDepartments.map((department) => {
            const techProgress = getChecklistProgress(department, 'technicians');
            const uatProgress = getChecklistProgress(department, 'uat');
            const rackPreparationProgress = getChecklistProgress(department, 'rack-preparation');
            const engineeringProgress = getChecklistProgress(department, 'engineering');
            const hasUnresolvedIssues = department.issues?.some(issue => issue.status !== 'Resolved');
            const openIssuesCount = department.issues?.filter(issue => issue.status === 'Open').length || 0;
            
            return (
              <div
                key={department.id}
                onClick={() => navigate(`/department/${department.id}`)}
                className={cn(
                  "group hover:bg-gray-50 transition-colors duration-150 cursor-pointer",
                  hasUnresolvedIssues && "bg-red-100 border border-red-300 hover:bg-red-200"
                )}
              >
                <div className="p-4">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors flex items-center gap-2">
                          <span>{department.name}</span>
                          {department.reference_number && (
                            <span className="text-gray-500 font-normal">Ref. #{department.reference_number}</span>
                          )}
                          {hasUnresolvedIssues && (
                            <TooltipProvider delayDuration={100}>
                              <Tooltip>
                                <TooltipTrigger>
                                  <AlertTriangle className="h-4 w-4 text-orange-500 flex-shrink-0" />
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{openIssuesCount} Open Issue{openIssuesCount !== 1 ? 's' : ''}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                        </h3>
                        {department.progress === 100 ? (
                          <CheckCircle2 className="h-5 w-5 text-green-500" />
                        ) : department.status === 'delayed' ? (
                          <AlertCircle className="h-5 w-5 text-yellow-500" />
                        ) : null}
                      </div>

                      {/* Mobile Progress View - Dynamic & Sorted */}
                      <div className="sm:hidden mt-2 space-y-1.5">
                        {department.checklists && department.checklists.length > 0 ? (
                          [...department.checklists] // Create a shallow copy before sorting
                            .sort((a, b) => formatChecklistType(a.type).localeCompare(formatChecklistType(b.type)))
                            .map(checklist => {
                              const progress = getChecklistProgress(department, checklist.type as ChecklistType);
                              const name = formatChecklistType(checklist.type);
                              return (
                                <div key={checklist.id}>
                                  <div className="flex items-center justify-between text-xs text-gray-600">
                                    <span className="font-medium">{name}</span>
                                    <span>{progress}%</span>
                                  </div>
                                  <div className="w-full h-1 bg-gray-200 rounded-full overflow-hidden">
                                    <div
                                      className="h-full rounded-full bg-blue-500" // Consider dynamic color based on progress
                                      style={{ width: `${progress}%` }}
                                    />
                                  </div>
                                </div>
                              );
                            })
                        ) : (
                          <p className="text-xs text-gray-500 text-center">No checklists assigned.</p>
                        )}
                      </div>

                      {/* Desktop Progress View - Dynamic & Sorted */}
                      <div className="hidden sm:flex sm:flex-wrap gap-x-4 gap-y-1 text-gray-600 mt-2">
                        {department.checklists && department.checklists.length > 0 ? (
                          [...department.checklists] // Create a shallow copy before sorting
                            .sort((a, b) => formatChecklistType(a.type).localeCompare(formatChecklistType(b.type)))
                            .map(checklist => {
                              const progress = getChecklistProgress(department, checklist.type as ChecklistType);
                              const name = formatChecklistType(checklist.type);
                              return (
                                <div key={checklist.id} className="flex items-center gap-2">
                                  <span className="text-sm font-medium">{name}:</span>
                                  <div className="w-16 h-1.5 bg-gray-200 rounded-full overflow-hidden">
                                    <div
                                      className="h-full rounded-full bg-blue-500" // Consider dynamic color
                                      style={{ width: `${progress}%` }}
                                    />
                                  </div>
                                  <span className="text-xs">{progress}%</span>
                                </div>
                              );
                            })
                        ) : (
                          <p className="text-sm text-gray-500">No checklists assigned.</p>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center gap-4 mt-2 sm:mt-0">
                      {/* Overall Progress indicator */}
                      <div className="flex-1 sm:flex-none">
                        <div className="text-sm font-medium text-gray-900 text-right">{department.progress}%</div>
                        <div className="w-full sm:w-24 h-1.5 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className={cn(
                              "h-full rounded-full transition-all duration-300",
                              getProgressColor(department.progress)
                            )}
                            style={{ width: `${department.progress}%` }}
                          />
                        </div>
                      </div>
                      
                      {/* Arrow indicator on hover */}
                      <ChevronRight className="h-5 w-5 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0" />
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
          {filteredDepartments.length === 0 && (
            <div className="p-4 text-center text-gray-500">
              No departments found matching your criteria.
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 