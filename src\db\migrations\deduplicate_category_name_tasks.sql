-- Deduplicate checklist items where description matches the category name.
-- Keeps one record per (checklist, category), prioritizing the header item if present.

BEGIN;

WITH duplicate_items AS (
  SELECT 
    ci.id,
    ROW_NUMBER() OVER (
      PARTITION BY ci.checklist_id, ci.category_id
      ORDER BY CASE WHEN ci.is_header THEN 0 ELSE 1 END, ci.id
    ) AS rn
  FROM checklist_items ci
  JOIN checklist_categories cc ON cc.id = ci.category_id
  WHERE ci.description = cc.name
)
DELETE FROM checklist_items
WHERE id IN (
  SELECT id FROM duplicate_items WHERE rn > 0
);

COMMIT; 