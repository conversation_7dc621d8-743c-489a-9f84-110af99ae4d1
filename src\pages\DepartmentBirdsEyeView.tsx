import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { cn } from '../lib/utils';
import { LoadingAnimation } from '@/components/LoadingAnimation';
import { usePageLoading } from '@/hooks/usePageLoading';

interface Department {
  id: string;
  name: string;
  progress: number;
  floor: number;
  reference_number: number;
}

const getProgressColor = (progress: number): string => {
  if (progress === 100) return 'bg-green-500';
  if (progress >= 75) return 'bg-yellow-500';
  if (progress >= 40) return 'bg-orange-500';
  return 'bg-red-500';
};

// Extract department display name from full name
const getDepartmentDisplayName = (name: string): string => {
  // Try to extract number from department name
  const match = name.match(/Department\s+(\d+[A-Z]?|ROC)\b/i);
  if (match) return match[1];
  
  // If it's a TEST or custom name, return the full name
  // We want to show the actual name, not reference number
  return name;
};

export function DepartmentBirdsEyeView() {
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const { loading, fadeOut } = usePageLoading(isInitialLoading);
  const navigate = useNavigate();
  const location = useLocation();
  const departments: Department[] = location.state?.departments || [];
  const courthouseName: string = location.state?.courthouseName || 'Stanley Mosk';
  
  useEffect(() => {
    async function loadData() {
      try {
        // Your data loading logic here
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setIsInitialLoading(false);
      }
    }

    loadData();
  }, []);

  if (loading) {
    return <LoadingAnimation fadeOut={fadeOut} />;
  }

  // Group departments by floor using the actual floor value
  const departmentsByFloor = departments.reduce((acc: Record<number, Department[]>, dept: Department) => {
    const floor = dept.floor || 1; // Fallback to floor 1 if not set
    
    if (!acc[floor]) {
      acc[floor] = [];
    }
    acc[floor].push(dept);
    return acc;
  }, {});

  // Sort floors in ascending order
  const sortedFloors = Object.keys(departmentsByFloor)
    .map(Number)
    .sort((a, b) => a - b);

  // Sort departments within each floor by reference number
  Object.keys(departmentsByFloor).forEach((floor: string) => {
    const floorNum = parseInt(floor);
    departmentsByFloor[floorNum].sort((a: Department, b: Department) => {
      // Sort by reference number if available
      if (a.reference_number && b.reference_number) {
        return a.reference_number - b.reference_number;
      }
      // Fallback to name-based sorting
      const aMatch = a.name.match(/\d+/);
      const bMatch = b.name.match(/\d+/);
      const aNum = aMatch ? parseInt(aMatch[0]) : 0;
      const bNum = bMatch ? parseInt(bMatch[0]) : 0;
      if (aNum === bNum) {
        return a.name.localeCompare(b.name);
      }
      return aNum - bNum;
    });
  });

  return (
    <div className="fixed inset-0 flex items-start sm:items-center justify-center bg-gray-50">
      <div className="w-full min-h-screen sm:min-h-0 h-full sm:w-[1400px] sm:h-[800px] bg-white sm:rounded-xl shadow-lg flex flex-col">
        <div className="sticky top-0 z-10 p-4 border-b flex items-center bg-white">
          <button 
            onClick={() => navigate(-1)}
            className="absolute left-4 p-2 text-gray-600 hover:text-gray-900 sm:hidden"
          >
            ←
          </button>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900 w-full text-center">
            {courthouseName} Birds Eye View
          </h1>
        </div>
        <div className="flex-1 overflow-auto p-3 sm:p-6">
          <div className="space-y-4 sm:space-y-6 py-4">
            {sortedFloors.map((floor) => (
              <div key={floor} className="space-y-2">
                <h2 className="text-base sm:text-lg font-semibold text-gray-900 px-2">Floor {floor}</h2>
                <div className="flex flex-wrap gap-1 bg-gray-100 p-2 rounded-lg">
                  {departmentsByFloor[floor].map((dept) => (
                    <button
                      key={dept.id}
                      onClick={() => navigate(`/department/${dept.id}`)}
                      className="relative group"
                    >
                      <div
                        className={cn(
                          "h-7 sm:h-8 px-2 sm:px-3 rounded border border-gray-200 flex items-center justify-center text-xs font-medium transition-all duration-200",
                          getProgressColor(dept.progress),
                          "hover:ring-2 hover:ring-blue-500 hover:scale-105 min-w-[2.5rem]"
                        )}
                      >
                        {getDepartmentDisplayName(dept.name)}
                      </div>
                      {/* Tooltip */}
                      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white px-2 py-1 rounded text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity z-10">
                        {dept.name}: {dept.progress}%
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
} 