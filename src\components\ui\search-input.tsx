import React from 'react';
import { Input } from './input';

interface SearchInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  onSearch: (value: string) => void;
  placeholder?: string;
}

export function SearchInput({ onSearch, placeholder = 'Search...', className, ...props }: SearchInputProps) {
  return (
    <div className="relative w-full max-w-sm">
      <Input
        type="search"
        placeholder={placeholder}
        className={`pl-9 ${className}`}
        onChange={(e) => onSearch(e.target.value)}
        {...props}
      />
      <svg
        className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
        />
      </svg>
    </div>
  );
} 