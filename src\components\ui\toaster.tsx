import { useToast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";

export function Toaster() {
  const { toasts } = useToast();

  return (
    <div
      className={cn(
        "fixed bottom-0 right-0 z-[100] flex max-h-screen w-full flex-col-reverse gap-2 p-4",
        "sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",
        "mobile:bottom-[env(safe-area-inset-bottom)]"
      )}
    >
      {toasts.map(({ id, title, description, variant = "default", open, ...props }) => {
        return (
          <div
            key={id}
            role="status"
            aria-live="polite"
            aria-atomic="true"
            className={cn(
              "group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border shadow-lg transition-opacity duration-300",
              variant === "destructive" 
                ? "bg-gradient-to-r from-red-600 to-red-500 text-white border-red-600" 
                : "bg-gradient-to-r from-blue-600 to-blue-500 text-white border-blue-600",
              open ? "opacity-100 animate-slide-in" : "opacity-0"
            )}
            {...props}
          >
            {/* Progress bar */}
            <div 
              className="absolute bottom-0 left-0 h-1 bg-white/20"
              style={{
                animation: `shrink ${props.duration || 5000}ms linear forwards`
              }}
            />
            
            <div className="flex flex-col gap-1 p-6 pr-8 w-full">
              {title && (
                <div className="text-sm font-semibold text-white/90">
                  {title}
                </div>
              )}
              {description && (
                <div className="text-sm text-white/80">
                  {description}
                </div>
              )}
            </div>
            
            <button
              onClick={() => props.onOpenChange?.(false)}
              className={cn(
                "absolute right-2 top-2 rounded-md p-1.5 text-white/70 opacity-0 transition-opacity hover:text-white focus:opacity-100 group-hover:opacity-100"
              )}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <line x1="18" y1="6" x2="6" y2="18" />
                <line x1="6" y1="6" x2="18" y2="18" />
              </svg>
            </button>
          </div>
        );
      })}
      
      <style>{`
        @keyframes shrink {
          from {
            width: 100%;
          }
          to {
            width: 0%;
          }
        }

        @keyframes slide-in {
          from {
            transform: translateX(100%);
          }
          to {
            transform: translateX(0);
          }
        }

        .animate-slide-in {
          animation: slide-in 0.3s cubic-bezier(0.16, 1, 0.3, 1);
        }
      `}</style>
    </div>
  );
} 