// Global user state management and authentication context
import React, { create<PERSON>ontext, use<PERSON>ontext, ReactNode, useState, useEffect } from 'react';
import { supabase, User, UserCompany } from '@/lib/supabase';

// Context value type definition
interface UserContextType {
  user: User | null;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  register: (username: string, password: string) => Promise<boolean>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<boolean>;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

// Provides user context to the application
export function UserProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(() => {
    // Try to restore user from localStorage on initial load
    const savedUser = localStorage.getItem('user');
    return savedUser ? JSON.parse(savedUser) : null;
  });

  // Effect to save user to localStorage whenever it changes
  useEffect(() => {
    if (user) {
      localStorage.setItem('user', JSON.stringify(user));
    } else {
      localStorage.removeItem('user');
    }
  }, [user]);

  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      const { data, error } = await supabase
        .rpc('login', {
          username,
          password
        });

      if (error) throw error;

      if (data) {
        const userData = {
          id: data.id,
          username: data.username,
          created_at: data.created_at,
          company: data.company as UserCompany // Add company to user data
        };
        setUser(userData);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  };

  const register = async (username: string, password: string): Promise<boolean> => {
    try {
      console.log('Attempting registration with:', { username });
      const { data: userId, error } = await supabase
        .rpc('register_user', {
          username,
          password
        });

      if (error) {
        if (error.message === 'Username already exists') {
          throw new Error('Username already exists. Please choose a different username.');
        }
        console.error('Registration RPC error:', error);
        throw error;
      }

      console.log('Registration successful, userId:', userId);
      // Return true for successful registration without auto-login
      return true;
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      setUser(null);
      localStorage.removeItem('user');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const changePassword = async (currentPassword: string, newPassword: string): Promise<boolean> => {
    try {
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .rpc('change_password', {
          p_user_id: user.id,
          p_current_password: currentPassword,
          p_new_password: newPassword
        });

      if (error) throw error;

      return data === true;
    } catch (error) {
      console.error('Change password error:', error);
      return false;
    }
  };

  return (
    <UserContext.Provider value={{ user, login, logout, register, changePassword }}>
      {children}
    </UserContext.Provider>
  );
}

// Custom hook for accessing user context
export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
}