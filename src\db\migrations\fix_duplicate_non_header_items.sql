-- Fixes duplicate category items appearing as tasks in rack-prep and engineering checklists.

-- Create a temporary table to identify categories with potential duplicates.
CREATE TEMP TABLE category_duplicates AS
SELECT 
  c.id AS checklist_id,
  c.type,
  ci.category_id,
  cc.name AS category_name,
  COUNT(*) FILTER (WHERE ci.is_header = true) AS header_count,
  COUNT(*) FILTER (WHERE ci.is_header = false AND ci.description = cc.name) AS duplicate_task_count
FROM 
  checklists c
JOIN 
  checklist_items ci ON c.id = ci.checklist_id
JOIN 
  checklist_categories cc ON ci.category_id = cc.id
WHERE 
  c.type IN ('rack-preparation', 'engineering')
GROUP BY 
  c.id, c.type, ci.category_id, cc.name
HAVING
  COUNT(*) FILTER (WHERE ci.is_header = false AND ci.description = cc.name) > 0;

-- Remove non-header items where description matches category name (for specific checklists).
DELETE FROM checklist_items
WHERE id IN (
  SELECT ci.id
  FROM checklist_items ci
  JOIN checklist_categories cc ON ci.category_id = cc.id
  JOIN checklists c ON ci.checklist_id = c.id
  WHERE 
    ci.is_header = false 
    AND ci.description = cc.name
    AND c.type IN ('rack-preparation', 'engineering')
);

-- Ensure categories in specific checklists have a header item (is_header=true, position=0).
INSERT INTO checklist_items (id, checklist_id, category_id, description, is_header, position)
SELECT 
  CONCAT(c.id, '_', cc.id, '_header_', md5(random()::text)) as id,
  c.id as checklist_id,
  cc.id as category_id,
  cc.name as description,
  true as is_header,
  0 as position
FROM 
  checklists c
JOIN
  checklist_items ci ON c.id = ci.checklist_id
JOIN
  checklist_categories cc ON ci.category_id = cc.id
LEFT JOIN
  checklist_items header ON 
    c.id = header.checklist_id 
    AND cc.id = header.category_id 
    AND header.is_header = true
WHERE 
  c.type IN ('rack-preparation', 'engineering')
  AND header.id IS NULL
GROUP BY
  c.id, cc.id, cc.name
ON CONFLICT DO NOTHING;

-- Renumber positions for specific checklists (headers first, then by original position/description).
WITH items_with_positions AS (
  SELECT 
    ci.id,
    ci.checklist_id,
    ci.category_id,
    ROW_NUMBER() OVER (
      PARTITION BY ci.checklist_id, ci.category_id 
      ORDER BY 
        ci.is_header DESC, -- Headers first
        ci.position NULLS LAST, -- Then by position
        ci.description -- Then alphabetically
    ) - 1 as new_position
  FROM 
    checklist_items ci
  JOIN
    checklists c ON ci.checklist_id = c.id
  WHERE
    c.type IN ('rack-preparation', 'engineering')
)
UPDATE checklist_items ci
SET position = iwp.new_position
FROM items_with_positions iwp
WHERE ci.id = iwp.id;

-- Clean up temporary table
DROP TABLE category_duplicates; 