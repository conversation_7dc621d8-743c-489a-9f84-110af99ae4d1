import React from 'react';
import { useNavigate } from 'react-router-dom';
import { MapPin, ChevronRight, Search } from 'lucide-react';
import type { Database } from '../types/supabase';
import { cn } from '../lib/utils';
import { CreateCourthouseDialog } from './CreateCourthouseDialog';
import { supabase } from '../lib/supabase';
import { useUser } from '@/contexts/UserContext';

type Courthouse = Database['public']['Tables']['courthouses']['Row'];

interface CourthouseListProps {
  courthouses: Courthouse[];
  projectId: string;
  onCourthouseCreated?: () => void;
}

export function CourthouseList({ courthouses, projectId, onCourthouseCreated }: CourthouseListProps) {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = React.useState('');
  const { user } = useUser();

  React.useEffect(() => {
    // Subscribe to real-time updates
    const channel = supabase
      .channel('courthouse-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'courthouses',
          filter: `project_id=eq.${projectId}`,
        },
        () => {
          // Refresh the list when changes occur
          onCourthouseCreated?.();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [projectId, onCourthouseCreated]);

  const filteredCourthouses = courthouses.filter(courthouse =>
    courthouse.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    courthouse.address.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="w-full max-w-5xl mx-auto bg-white/30 backdrop-blur-lg rounded-xl shadow-lg border border-gray-200">
      <div className="sticky top-0 z-10 bg-white/80 backdrop-blur-sm p-4 border-b border-gray-200 rounded-t-xl">
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search courthouses..."
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white/50"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          {user?.company === 'admin' && (
            <CreateCourthouseDialog 
              projectId={projectId}
              onCourthouseCreated={onCourthouseCreated}
            />
          )}
        </div>
      </div>

      <div className="h-[calc(100vh-22rem)] overflow-y-auto">
        <div className="divide-y divide-gray-100">
          {filteredCourthouses.map((courthouse) => (
            <div
              key={courthouse.id}
              onClick={() => navigate(`/courthouse/${courthouse.id}`)}
              className="group hover:bg-gray-50 transition-colors duration-150 cursor-pointer"
            >
              <div className="p-4">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                      {courthouse.name}
                    </h3>
                    <div className="flex items-center space-x-2 text-gray-600 mt-1">
                      <MapPin className="h-4 w-4 flex-shrink-0" />
                      <span className="text-sm truncate">{courthouse.address}</span>
                    </div>
                  </div>

                  <div className="hidden sm:flex items-center space-x-4">
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900">{courthouse.progress}%</div>
                      <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div
                          className={cn(
                            "h-full rounded-full transition-all duration-300",
                            courthouse.progress >= 80 ? "bg-green-500" :
                            courthouse.progress >= 50 ? "bg-blue-500" :
                            courthouse.progress >= 20 ? "bg-yellow-500" : "bg-red-500"
                          )}
                          style={{ width: `${courthouse.progress}%` }}
                        />
                      </div>
                    </div>
                    
                    <ChevronRight className="h-5 w-5 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </div>

                  <div className="sm:hidden flex justify-end">
                    <ChevronRight className="h-5 w-5 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </div>
                </div>
              </div>
            </div>
          ))}
          {filteredCourthouses.length === 0 && (
            <div className="p-4 text-center text-gray-500">
              No courthouses found matching your criteria.
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 