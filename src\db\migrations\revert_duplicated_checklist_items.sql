-- Revert duplicate checklist items (created by a previous migration).
-- Focuses on rack-preparation and engineering checklists.

-- Identify duplicates for debugging (optional, can be commented out in production).
WITH duplicates AS (
  SELECT
    category_id,
    checklist_id,
    description,
    COUNT(*),
    array_agg(id) as ids
  FROM checklist_items
  WHERE checklist_id IN (
    SELECT id FROM checklists WHERE type IN ('rack-preparation', 'engineering')
  )
  GROUP BY category_id, checklist_id, description
  HAVING COUNT(*) > 1
)
SELECT * FROM duplicates;

-- Keep the oldest item (first created) for each duplicate group and delete the rest.
WITH ranked_duplicates AS (
  SELECT 
    id,
    checklist_id,
    category_id,
    description,
    completed,
    ROW_NUMBER() OVER (
      PARTITION BY checklist_id, category_id, description
      ORDER BY created_at ASC
    ) as row_num
  FROM checklist_items ci
  WHERE ci.checklist_id IN (
    SELECT id FROM checklists WHERE type IN ('rack-preparation', 'engineering')
  )
)
DELETE FROM checklist_items
WHERE id IN (
  SELECT id FROM ranked_duplicates
  WHERE row_num > 1
);

-- Remove duplicate category headers, keeping the oldest.
WITH ranked_headers AS (
  SELECT 
    id,
    checklist_id,
    category_id,
    ROW_NUMBER() OVER (
      PARTITION BY checklist_id, category_id
      ORDER BY created_at ASC
    ) as row_num
  FROM checklist_items ci
  WHERE ci.is_header = true
  AND ci.checklist_id IN (
    SELECT id FROM checklists WHERE type IN ('rack-preparation', 'engineering')
  )
)
DELETE FROM checklist_items
WHERE id IN (
  SELECT id FROM ranked_headers
  WHERE row_num > 1
);

-- Clean up items belonging to categories that no longer have a header.
DELETE FROM checklist_items
WHERE is_header = false
AND checklist_id IN (SELECT id FROM checklists WHERE type IN ('rack-preparation', 'engineering'))
AND category_id NOT IN (
  SELECT category_id
  FROM checklist_items
  WHERE is_header = true
  AND checklist_id = checklist_items.checklist_id
);

-- Renumber positions sequentially within each category (headers first).
WITH ranked_items AS (
  SELECT 
    ci.id,
    ci.checklist_id,
    ci.category_id,
    ci.is_header,
    ROW_NUMBER() OVER (
      PARTITION BY ci.checklist_id, ci.category_id
      ORDER BY ci.is_header DESC, ci.position, ci.created_at
    ) - 1 AS new_position
  FROM checklist_items ci
  JOIN checklists c ON ci.checklist_id = c.id
  WHERE c.type IN ('rack-preparation', 'engineering')
)
UPDATE checklist_items ci
SET position = ri.new_position
FROM ranked_items ri
WHERE ci.id = ri.id
AND ci.position != ri.new_position; 