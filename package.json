{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "supabase:start": "supabase start", "supabase:stop": "supabase stop", "supabase:migrate": "supabase db push", "supabase:types": "supabase gen types typescript --local > types/supabase.ts", "generate:supabase-types": "npx supabase gen types typescript --local > src/types/supabase.ts"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-toast": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.0", "@supabase/supabase-js": "^2.47.12", "@types/react-beautiful-dnd": "^13.1.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "drizzle-orm": "^0.41.0", "embla-carousel-react": "^8.5.2", "framer-motion": "^11.18.0", "lucide-react": "^0.344.0", "postgres": "^3.4.5", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-router-dom": "^6.22.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@shadcn/ui": "^0.0.4", "@types/node": "^22.10.5", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "drizzle-kit": "^0.30.6", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "supabase": "^2.9.6", "tailwindcss": "^3.4.1", "tsx": "^4.19.3", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}