import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronRight, Search } from 'lucide-react';
import type { Database } from '../types/supabase';
import { cn } from '../lib/utils';

type Project = Database['public']['Tables']['projects']['Row'];

interface ProjectListProps {
  projects: Project[];
}

export function ProjectList({ projects }: ProjectListProps) {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = React.useState('');

  const filteredProjects = projects.filter(project =>
    project.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="w-full max-w-5xl mx-auto bg-white/30 backdrop-blur-lg rounded-xl shadow-lg border border-gray-200">
      {/* Search and filters header - sticky */}
      <div className="sticky top-0 z-10 bg-white/80 backdrop-blur-sm p-4 border-b border-gray-200 rounded-t-xl">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Search projects..."
            className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white/50"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* Scrollable list container */}
      <div className="h-[calc(100vh-22rem)] overflow-y-auto">
        <div className="divide-y divide-gray-100">
          {filteredProjects.map((project) => (
            <div
              key={project.id}
              onClick={() => navigate(`/project/${project.id}`)}
              className="group hover:bg-gray-50 transition-colors duration-150 cursor-pointer"
            >
              <div className="p-4 flex items-center justify-between">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                    {project.name}
                  </h3>
                  <div className="flex items-center space-x-2 text-gray-600 mt-1">
                    <span className="text-sm">Status: {project.status}</span>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  {/* Progress indicator */}
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">{project.progress}%</div>
                    <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div
                        className={cn(
                          "h-full rounded-full transition-all duration-300",
                          project.progress >= 80 ? "bg-green-500" :
                          project.progress >= 50 ? "bg-blue-500" :
                          project.progress >= 20 ? "bg-yellow-500" : "bg-red-500"
                        )}
                        style={{ width: `${project.progress}%` }}
                      />
                    </div>
                  </div>
                  
                  {/* Arrow indicator on hover */}
                  <ChevronRight className="h-5 w-5 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
} 