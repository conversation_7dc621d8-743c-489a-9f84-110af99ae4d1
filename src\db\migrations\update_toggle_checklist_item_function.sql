-- Update the toggle_checklist_item function to prevent unchecking tasks completed by others

-- Drop the old function if it exists
DROP FUNCTION IF EXISTS public.toggle_checklist_item(text, text);

-- Create the updated function
CREATE OR REPLACE FUNCTION public.toggle_checklist_item(p_item_id text, p_username text)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    item_record record;
    result json;
BEGIN
    -- Fetch current checklist item state
    SELECT 
        id,
        completed,
        completed_by_username
    INTO item_record
    FROM checklist_items
    WHERE id = p_item_id;
    
    -- If item not found, return error
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Checklist item not found';
    END IF;
    
    -- Check if attempting to uncheck another user's completed task
    IF item_record.completed = true AND 
       item_record.completed_by_username IS NOT NULL AND 
       item_record.completed_by_username != p_username THEN
        RAISE EXCEPTION 'Cannot uncheck a task completed by another user';
    END IF;
    
    -- Toggle completion status and record user/timestamp
    UPDATE checklist_items
    SET 
        completed = NOT completed,
        completed_by_username = CASE 
                                  WHEN NOT completed THEN p_username 
                                  ELSE NULL 
                                END,
        completed_at = CASE 
                         WHEN NOT completed THEN now() 
                         ELSE NULL 
                       END,
        updated_at = now()
    WHERE id = p_item_id
    RETURNING id, completed, completed_by_username, completed_at INTO item_record;
    
    -- Return the updated item as JSON
    result := json_build_object(
        'id', item_record.id,
        'completed', item_record.completed,
        'completed_by_username', item_record.completed_by_username,
        'completed_at', item_record.completed_at
    );
    
    RETURN result;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.toggle_checklist_item(text, text) TO authenticated;
