import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { ChevronLeft, ChevronDown } from 'lucide-react';
import { Button } from '../components/ui/button';
import { format } from 'date-fns';
import { cn } from '../lib/utils';
import { LoadingAnimation } from '../components/LoadingAnimation';
import { usePageLoading } from '../hooks/usePageLoading';
import { getDepartments } from '../lib/db';
import { Badge } from '../components/ui/badge';
import { Card } from '../components/ui/card';
import type { Database } from '../types/supabase';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../components/ui/select";

type Department = Database['public']['Tables']['departments']['Row'] & {
  issues?: Array<{
    id: string;
    title: string;
    content: string;
    status: 'Open' | 'In Progress' | 'Resolved';
    created_by_username: string;
    created_at: string;
    task_id?: string;
    task_description?: string;
    checklist_type?: string;
    category_name?: string;
    department_id: string;
  }>;
};

interface Issue {
  id: string;
  title: string;
  content: string;
  status: 'Open' | 'In Progress' | 'Resolved';
  created_by_username: string;
  created_at: string;
  task_id?: string;
  task_description?: string;
  checklist_type?: string;
  category_name?: string;
  departmentName: string;
  departmentId: string;
}

interface LocationState {
  courthouseName: string;
  courthouseId: string;
}

const IssueStatusBadge: React.FC<{ status: 'Open' | 'In Progress' | 'Resolved' }> = ({ status }) => {
  const colors = {
    'Open': 'bg-blue-100 text-blue-800 border-blue-200',
    'In Progress': 'bg-purple-100 text-purple-800 border-purple-200',
    'Resolved': 'bg-gray-100 text-gray-800 border-gray-200',
  };

  return (
    <Badge className={cn(
      'px-2 py-1 text-xs font-medium rounded-full border',
      colors[status]
    )}>
      {status}
    </Badge>
  );
};

export const IssueLogPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { courthouseName, courthouseId } = (location.state as LocationState) || {};
  const [selectedStatus, setSelectedStatus] = useState<'Open' | 'In Progress' | 'Resolved' | 'All'>('All');
  const [allIssues, setAllIssues] = useState<Issue[]>([]);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const { loading, fadeOut } = usePageLoading(isInitialLoading);

  useEffect(() => {
    const loadData = async () => {
      try {
        const departments = await getDepartments(courthouseId);
        const issues = departments.flatMap((dept: Department) => 
          (dept.issues || []).map(issue => ({
            ...issue,
            departmentName: dept.name,
            departmentId: issue.department_id || dept.id
          }))
        );
        setAllIssues(issues);
      } catch (error) {
        console.error('Error loading issues:', error);
      } finally {
        setIsInitialLoading(false);
      }
    };

    if (courthouseId) {
      loadData();
    }
  }, [courthouseId]);

  const filteredIssues = allIssues
    .filter(issue => selectedStatus === 'All' || issue.status === selectedStatus)
    .sort((a, b) => {
      // First, sort by status (Open first, then In Progress, then Resolved)
      const statusOrder = { 'Open': 0, 'In Progress': 1, 'Resolved': 2 };
      const statusDiff = statusOrder[a.status] - statusOrder[b.status];
      if (statusDiff !== 0) return statusDiff;
      
      // Then sort by creation date (newest first)
      return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    });

  const handleIssueClick = (issue: Issue) => {
    navigate(`/department/${issue.departmentId}`, {
      state: {
        taskContext: {
          taskId: issue.task_id,
          taskTitle: issue.title,
          taskDescription: issue.content,
          taskStatus: issue.status,
          checklistType: issue.checklist_type
        }
      }
    });
  };

  if (loading) {
    return <LoadingAnimation fadeOut={fadeOut} />;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            className="p-2"
            onClick={() => navigate(-1)}
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold">{courthouseName} - Issue Log</h1>
        </div>
        <Select
          value={selectedStatus}
          onValueChange={(value: string) => setSelectedStatus(value as 'Open' | 'In Progress' | 'Resolved' | 'All')}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All">All Issues</SelectItem>
            <SelectItem value="Open">Open</SelectItem>
            <SelectItem value="In Progress">In Progress</SelectItem>
            <SelectItem value="Resolved">Resolved</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="relative h-[calc(100vh-12rem)] mx-auto max-w-4xl">
        <div className="absolute inset-0 overflow-y-auto rounded-lg border border-gray-200 bg-white/30 backdrop-blur-lg shadow-lg">
          <div className="p-4 space-y-4">
            {filteredIssues.map((issue) => (
              <Card
                key={issue.id}
                className="p-4 cursor-pointer hover:shadow-lg transition-shadow"
                onClick={() => handleIssueClick(issue)}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-lg font-semibold mb-2">{issue.title}</h3>
                    <p className="text-gray-600 mb-2">{issue.content}</p>
                    <p className="text-sm text-gray-500">
                      Department: {issue.departmentName}
                      {issue.checklist_type && (
                        <>
                          <span className="mx-2">•</span>
                          {issue.checklist_type}
                        </>
                      )}
                      {issue.category_name && (
                        <>
                          <span className="mx-2">•</span>
                          {issue.category_name}
                        </>
                      )}
                    </p>
                  </div>
                  <div className="flex flex-col items-end space-y-2">
                    <IssueStatusBadge status={issue.status} />
                    <span className="text-sm text-gray-500">
                      {new Date(issue.created_at).toLocaleDateString()}
                    </span>
                    <span className="text-xs text-gray-400">
                      by {issue.created_by_username}
                    </span>
                  </div>
                </div>
              </Card>
            ))}
            {filteredIssues.length === 0 && (
              <p className="text-center text-gray-500 py-8">No issues found</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}; 