import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { createPortal } from 'react-dom';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useUser } from '@/contexts/UserContext';
import { supabase } from '@/lib/supabase';
import { Plus, Trash2, GripVertical, ChevronDown, ChevronRight, ChevronLeft, Settings, X, Pencil } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import useEmblaCarousel from 'embla-carousel-react';
import { cn } from '@/lib/utils';
import type { Database } from '../types/supabase';

interface ChecklistTemplateItem {
  id: string;
  type: string;
  description: string;
  category_id: string;
  position: number;
  created_at?: string;
  updated_at?: string;
}

interface ChecklistCategory {
  id: string;
  name: string;
  created_at?: string;
  updated_at?: string;
}

interface ChecklistTemplate {
  type: string;
  items: ChecklistTemplateItem[];
}

interface DeletionState {
  itemId: string;
  templateType: string;
  inProgress: boolean;
}

const AddTaskModal = ({
  isOpen,
  onClose,
  onAdd,
  categoryName,
}: {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (description: string) => void;
  categoryName: string;
}) => {
  const [description, setDescription] = useState('');
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isOpen || !isClient) return null;

  return createPortal(
    <div className="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="flex justify-between items-center border-b px-6 py-4">
          <h3 className="text-lg font-semibold text-gray-800">
            Add Task to {categoryName}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        <div className="p-6">
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Task Description
            </label>
            <Input
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter task description"
              className="w-full"
              autoFocus
            />
          </div>
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (description.trim()) {
                  onAdd(description.trim());
                  setDescription('');
                  onClose();
                }
              }}
            >
              Add Task
            </Button>
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
};

const EditTaskModal = ({
  isOpen,
  onClose,
  onSave,
  initialDescription,
  categoryName,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSave: (description: string) => void;
  initialDescription: string;
  categoryName: string;
}) => {
  const [description, setDescription] = useState(initialDescription);
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isOpen || !isClient) return null;

  return createPortal(
    <div className="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="flex justify-between items-center border-b px-6 py-4">
          <h3 className="text-lg font-semibold text-gray-800">
            Edit Task in {categoryName}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        <div className="p-6">
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Task Description
            </label>
            <Input
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter task description"
              className="w-full"
              autoFocus
            />
          </div>
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (description.trim()) {
                  onSave(description.trim());
                  onClose();
                }
              }}
            >
              Save Changes
            </Button>
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
};

const CreateHeaderModal = ({
  isOpen,
  onClose,
  onSave,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSave: (description: string) => void;
}) => {
  const [description, setDescription] = useState('');
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isOpen || !isClient) return null;

  return createPortal(
    <div className="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="flex justify-between items-center border-b px-6 py-4">
          <h3 className="text-lg font-semibold text-gray-800">
            Create New Category
          </h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        <div className="p-6">
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Category Name
            </label>
            <Input
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter category name"
              className="w-full"
              autoFocus
            />
          </div>
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (description.trim()) {
                  onSave(description.trim());
                  setDescription('');
                }
              }}
            >
              Create Category
            </Button>
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
};

const CreateChecklistModal = ({
  isOpen,
  onClose,
  onSave,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSave: (name: string) => void;
}) => {
  const [checklistName, setChecklistName] = useState('');
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    if (isOpen) {
      setChecklistName('');
    }
    setIsClient(true);
  }, [isOpen]);

  if (!isOpen || !isClient) return null;

  return createPortal(
    <div className="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="flex justify-between items-center border-b px-6 py-4">
          <h3 className="text-lg font-semibold text-gray-800">
            Create New Checklist
          </h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        <div className="p-6">
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Checklist Name
            </label>
            <Input
              value={checklistName}
              onChange={(e) => setChecklistName(e.target.value)}
              placeholder="Enter checklist name"
              className="w-full"
              autoFocus
            />
          </div>
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (checklistName.trim()) {
                  onSave(checklistName.trim());
                  setChecklistName('');
                  onClose();
                }
              }}
              disabled={!checklistName.trim()}
            >
              Create Checklist
            </Button>
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
};

const DeleteChecklistModal = ({
  isOpen,
  onClose,
  onConfirm,
  checklistType,
}: {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  checklistType: string;
}) => {
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    setIsClient(true);
  }, []);

  const title = checklistType
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  if (!isOpen || !isClient) return null;

  return createPortal(
    <div className="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="flex justify-between items-center border-b px-6 py-4">
          <h3 className="text-lg font-semibold text-gray-800">
            Delete {title}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        <div className="p-6">
          <div className="mb-6">
            <p className="text-gray-700 mb-4">
              Are you sure you want to delete the <strong>{title}</strong>? This will remove all template items and categories associated with this checklist type.
            </p>
            <p className="text-red-600 text-sm">
              Warning: This action cannot be undone and may affect existing departments that use this checklist type.
            </p>
          </div>
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={onConfirm}
            >
              Delete Checklist
            </Button>
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
};

const RenameCategoryModal = ({
  isOpen,
  onClose,
  onSave,
  initialName,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSave: (newName: string) => void;
  initialName: string;
}) => {
  const [name, setName] = useState(initialName);
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    if (isOpen) {
        setName(initialName);
    }
    setIsClient(true);
  }, [isOpen, initialName]);

  if (!isOpen || !isClient) return null;

  return createPortal(
    <div className="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="flex justify-between items-center border-b px-6 py-4">
          <h3 className="text-lg font-semibold text-gray-800">
            Rename Category
          </h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        <div className="p-6">
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              New Category Name
            </label>
            <Input
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter new category name"
              className="w-full"
              autoFocus
            />
          </div>
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (name.trim() && name.trim() !== initialName) {
                  onSave(name.trim());
                  onClose();
                } else if (name.trim() === initialName) {
                    onClose();
                }
              }}
              disabled={!name.trim()}
            >
              Save Changes
            </Button>
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
};

// Sortable item component
function SortableItem({
  item,
  categories,
  onDelete,
  onEdit,
  deletingItems
}: {
  item: ChecklistTemplateItem;
  categories: ChecklistCategory[];
  onDelete: (id: string) => void;
  onEdit: (id: string, description: string) => void;
  deletingItems: Set<string>;
}) {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const { user } = useUser();
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: item.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : deletingItems.has(item.id) ? 0.3 : 1,
  };

  return (
    <>
      <div
        ref={setNodeRef}
        style={style}
        className={cn(
          "flex items-center gap-2 bg-gray-50 p-3 rounded-lg border",
          "transition-all duration-200",
          deletingItems.has(item.id) && "opacity-30"
        )}
      >
        <div {...attributes} {...listeners} className="cursor-grab">
          <GripVertical className="h-5 w-5 text-gray-400" />
        </div>
        <div className="flex-1">
          <p className="text-sm">{item.description}</p>
          <p className="text-xs text-gray-500">
            {categories.find(c => c.id === item.category_id)?.name}
          </p>
        </div>
        <div className="flex gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsEditModalOpen(true)}
            disabled={deletingItems.has(item.id)}
          >
            <Pencil className="h-4 w-4 text-blue-500" />
          </Button>
          {user?.company === 'admin' && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(item.id)}
              disabled={deletingItems.has(item.id)}
            >
              <Trash2 className="h-4 w-4 text-red-500" />
            </Button>
          )}
        </div>
      </div>

      <EditTaskModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onSave={(description) => onEdit(item.id, description)}
        initialDescription={item.description}
        categoryName={categories.find(c => c.id === item.category_id)?.name || ''}
      />
    </>
  );
}

// Checklist Panel Component
const ChecklistPanel = ({
  type,
  template,
  templates,
  categories,
  onDelete,
  onEdit,
  onAddItem,
  onDragEnd,
  deletingItems,
  setCategories,
  setTemplates,
  onDeleteChecklist,
  onRenameCategory
}: {
  type: string;
  template: ChecklistTemplate;
  templates: ChecklistTemplate[];
  categories: ChecklistCategory[];
  onDelete: (id: string) => void;
  onEdit: (id: string, description: string) => void;
  onAddItem: (templateType: string, description: string, categoryId: string) => void;
  onDragEnd: (event: DragEndEvent, templateType: string) => void;
  deletingItems: Set<string>;
  setCategories: React.Dispatch<React.SetStateAction<ChecklistCategory[]>>;
  setTemplates: React.Dispatch<React.SetStateAction<ChecklistTemplate[]>>;
  onDeleteChecklist: (type: string) => void;
  onRenameCategory: (categoryId: string, newName: string) => Promise<void>;
}) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [headerModalOpen, setHeaderModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<{ id: string; name: string } | null>(null);
  const [renameModalOpen, setRenameModalOpen] = useState(false);
  const [categoryToRename, setCategoryToRename] = useState<ChecklistCategory | null>(null);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const title = type
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  const groupedItems = new Map<string, ChecklistTemplateItem[]>();
  template.items.forEach(item => {
    const categoryId = item.category_id;
    if (!groupedItems.has(categoryId)) {
      groupedItems.set(categoryId, []);
    }
    groupedItems.get(categoryId)?.push(item);
  });

  const generateLazyId = (name: string): string => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '_')
      .replace(/^_+|_+$/g, '')
      .substring(0, 50);
  };

  const handleOpenRenameModal = (category: ChecklistCategory) => {
    setCategoryToRename(category);
    setRenameModalOpen(true);
  };

  const handleCloseRenameModal = () => {
    setCategoryToRename(null);
    setRenameModalOpen(false);
  };

  const handleSaveRename = async (newName: string) => {
    if (categoryToRename) {
      await onRenameCategory(categoryToRename.id, newName);
    }
    handleCloseRenameModal();
  };

  const handleCreateHeader = async (description: string) => {
    if (!description.trim()) {
      toast({
        title: 'Error',
        description: 'Category name cannot be empty',
        variant: 'destructive',
      });
      return;
    }

    const timestamp = new Date().getTime();
    const randomSuffix = Math.random().toString(36).substring(2, 7);
    const categoryId = `${generateLazyId(description)}_${timestamp}_${randomSuffix}`;

    try {
      const now = new Date().toISOString();

      const { data: categoryData, error: categoryError } = await supabase
        .from('checklist_categories')
        .insert({
          id: categoryId,
          name: description,
          created_at: now,
          updated_at: now
        })
        .select()
        .single();

      if (categoryError) {
        throw categoryError;
      }

      const { data: templateData, error: templateError } = await supabase
        .from('checklist_templates')
        .insert({
          type: type,
          category_id: categoryId,
          description: description,
          position: template.items.length,
          created_at: now,
          updated_at: now
        })
        .select()
        .single();

      if (templateError) {
        throw templateError;
      }

      const { data: existingChecklists, error: checklistsError } = await supabase
        .from('checklists')
        .select(`
          id,
          department_id,
          type,
          created_at
        `)
        .eq('type', type)
        .order('created_at', { ascending: true });

      if (checklistsError) {
        throw checklistsError;
      }

      console.log(`Found ${existingChecklists?.length || 0} checklists of type ${type} to update`);

      if (existingChecklists && existingChecklists.length > 0) {
        const checklistItems = existingChecklists.map(checklist => {
          const itemId = `${checklist.id}_${categoryId}_${timestamp}_${randomSuffix}`;
          return {
            id: itemId,
            checklist_id: checklist.id,
            category_id: categoryId,
            description: description,
            completed: false,
            is_header: true,
            position: template.items.length,
            template_id: templateData.id,
            created_at: now,
            updated_at: now
          };
        });

        const { error: itemsError } = await supabase
          .from('checklist_items')
          .insert(checklistItems);

        if (itemsError) {
          console.error('Error creating checklist items:', itemsError);
          throw itemsError;
        }
      }

      setCategories(prev => [...prev, categoryData]);

      const newTemplate = {
        ...template,
        items: [
          ...template.items,
          {
            ...templateData,
            type: type,
            category_id: categoryId,
            description: description,
            position: template.items.length
          }
        ]
      };

      const templateIndex = templates.findIndex(t => t.type === type);
      if (templateIndex !== -1) {
        const newTemplates = [...templates];
        newTemplates[templateIndex] = newTemplate;
        setTemplates(newTemplates);
      }

      setHeaderModalOpen(false);

      toast({
        title: 'Category Created Successfully',
        description: `The category "${description}" has been created and propagated to all existing checklists.`,
      });
    } catch (err: any) {
      console.error('Error while creating category:', err);
      toast({
        title: 'Error',
        description: err.message || 'Unexpected error occurred while creating category',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="flex-[0_0_100%] sm:flex-[0_0_45%] min-w-0 px-2">
      <div className="bg-white rounded-lg shadow-sm flex flex-col h-[calc(100vh-20rem)] sm:h-[calc(100vh-20rem)]">
        <div className="border-b px-4 sm:px-6 py-4 flex justify-between items-center">
          <div className="flex items-center gap-2">
            <h3 className="text-base sm:text-lg font-semibold text-gray-800">
              {title} Template Management
            </h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDeleteChecklist(type)}
              className="text-red-500 hover:text-red-700 hover:bg-red-50"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setHeaderModalOpen(true)}
            className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Category
          </Button>
        </div>
        <div className="flex-1 overflow-y-auto">
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={(event) => {
              onDragEnd(event, type);
            }}
          >
            <div className="divide-y">
              {Array.from(groupedItems.entries()).map(([categoryId, items]) => {
                const category = categories.find(c => c.id === categoryId);
                if (!category) return null;

                return (
                  <div key={categoryId} className="divide-y">
                    <div className="px-4 sm:px-6 py-2 bg-gray-50 flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <h4 className="text-sm font-medium text-gray-700">
                          {category.name}
                        </h4>
                        <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleOpenRenameModal(category)}
                            className="h-6 w-6 text-gray-500 hover:text-blue-600 hover:bg-blue-50"
                          >
                          <Pencil className="h-4 w-4" />
                        </Button>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedCategory(category);
                          setModalOpen(true);
                        }}
                        className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Add Task
                      </Button>
                    </div>
                    <SortableContext
                      items={template.items.map(item => item.id)}
                      strategy={verticalListSortingStrategy}
                    >
                      <div className="space-y-2 p-4">
                        {items.length === 0 ? (
                          <div className="text-center py-4 text-gray-500">
                            <p className="text-sm">No tasks in this category yet.</p>
                            <p className="text-xs mt-1">Click "Add Task" above to create your first task.</p>
                          </div>
                        ) : (
                          items.map((item) => (
                            <SortableItem
                              key={item.id}
                              item={item}
                              categories={categories}
                              onDelete={onDelete}
                              onEdit={onEdit}
                              deletingItems={deletingItems}
                            />
                          ))
                        )}
                      </div>
                    </SortableContext>
                  </div>
                );
              })}
            </div>
          </DndContext>
        </div>
      </div>

      <AddTaskModal
        isOpen={modalOpen}
        onClose={() => {
          setModalOpen(false);
          setSelectedCategory(null);
        }}
        onAdd={(description) => {
          if (selectedCategory) {
            onAddItem(type, description, selectedCategory.id);
          }
        }}
        categoryName={selectedCategory?.name || ''}
      />

      <CreateHeaderModal
        isOpen={headerModalOpen}
        onClose={() => setHeaderModalOpen(false)}
        onSave={handleCreateHeader}
      />

      <RenameCategoryModal
        isOpen={renameModalOpen}
        onClose={handleCloseRenameModal}
        onSave={handleSaveRename}
        initialName={categoryToRename?.name || ''}
      />
    </div>
  );
};

export function ChecklistManagementPage() {
  const navigate = useNavigate();
  const { user } = useUser();
  const [templates, setTemplates] = useState<ChecklistTemplate[]>([]);
  const [categories, setCategories] = useState<ChecklistCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: 'start',
    containScroll: 'trimSnaps',
    dragFree: true,
    slidesToScroll: 1,
    skipSnaps: false
  });
  const [selectedChecklistIndex, setSelectedChecklistIndex] = useState(0);

  const [deletingItems, setDeletingItems] = useState<Set<string>>(new Set());
  const deletionRef = useRef<DeletionState | null>(null);
  const unmountedRef = useRef(false);

  const [createChecklistModalOpen, setCreateChecklistModalOpen] = useState(false);
  const [deleteChecklistModalOpen, setDeleteChecklistModalOpen] = useState(false);
  const [checklistToDelete, setChecklistToDelete] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    if (user?.company !== 'admin') {
      navigate('/');
    }
  }, [user, navigate]);

  useEffect(() => {
    const loadData = async () => {
      try {
        const { data: categoriesData, error: categoriesError } = await supabase
          .from('checklist_categories')
          .select('*')
          .order('name');

        if (categoriesError) throw categoriesError;
        setCategories(categoriesData);

        const { data: templatesData, error: templatesError } = await supabase
          .from('checklist_templates')
          .select('*')
          .order('type')
          .order('position');

        if (templatesError) throw templatesError;

        const groupedTemplates = templatesData.reduce((acc: ChecklistTemplate[], item: ChecklistTemplateItem) => {
          const existingTemplate = acc.find(t => t.type === item.type);
          if (existingTemplate) {
            existingTemplate.items.push(item);
          } else {
            acc.push({
              type: item.type,
              items: [item]
            });
          }
          return acc;
        }, []);

        groupedTemplates.forEach((template: ChecklistTemplate) => {
          template.items.sort((a: ChecklistTemplateItem, b: ChecklistTemplateItem) => a.position - b.position);
        });

        setTemplates(groupedTemplates);
      } catch (error) {
        console.error('Error loading data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load templates. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    void loadData();
  }, []);

  useEffect(() => {
    if (emblaApi) {
      emblaApi.on('select', () => {
        setSelectedChecklistIndex(emblaApi.selectedScrollSnap());
      });
    }
  }, [emblaApi]);

  useEffect(() => {
    return () => {
      unmountedRef.current = true;
    };
  }, []);

  const handleDragEnd = async (event: DragEndEvent, templateType: string) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;

    const template = templates.find(t => t.type === templateType);
    if (!template) {
        console.error(`Template not found for type: ${templateType}`);
        toast({ title: 'Error', description: 'Could not find the template to reorder items.', variant: 'destructive' });
        return;
    }

    const oldIndex = template.items.findIndex(item => item.id === active.id);
    const newIndex = template.items.findIndex(item => item.id === over.id);

    const newItems = arrayMove(template.items, oldIndex, newIndex);
    const updatedItems = newItems.map((item, index) => ({
      ...item,
      position: index
    }));

    setTemplates(prev => prev.map(t => {
      if (t.type === templateType) {
        return {
          ...t,
          items: updatedItems
        };
      }
      return t;
    }));

    try {
      for (const item of updatedItems) {
        console.log('Updating item:', item);
        const { error } = await supabase
          .from('checklist_templates')
          .update({
            position: item.position,
            type: templateType,
            category_id: item.category_id,
            description: item.description
          })
          .eq('id', item.id);

        if (error) {
          console.error('Error updating item:', item, error);
          throw error;
        }
      }

      toast({
        title: 'Success',
        description: 'Template positions updated successfully.',
      });
    } catch (error) {
      console.error('Error updating positions:', error);

      setTemplates(prev => prev.map(t => {
        if (t.type === templateType) {
          return {
            ...t,
            items: template.items
          };
        }
        return t;
      }));

      toast({
        title: 'Error',
        description: 'Failed to update template positions. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleAddItem = async (templateType: string, description: string, categoryId: string) => {
    try {
      const template = templates.find(t => t.type === templateType);
      if (!template) return;

      const maxPosition = Math.max(...template.items.map(item => item.position), -1);
      const now = new Date().toISOString();

      const { data: templateData, error: templateError } = await supabase
        .from('checklist_templates')
        .insert({
          type: templateType,
          description: description,
          category_id: categoryId,
          position: maxPosition + 1,
          created_at: now,
          updated_at: now
        })
        .select()
        .single();

      if (templateError) throw templateError;

      const { data: existingChecklists, error: checklistsError } = await supabase
        .from('checklists')
        .select('id')
        .eq('type', templateType);

      if (checklistsError) throw checklistsError;

      if (existingChecklists && existingChecklists.length > 0) {
        const timestamp = new Date().getTime();
        const randomSuffix = Math.random().toString(36).substring(2, 7);

        const checklistItems = existingChecklists.map(checklist => ({
          id: `${checklist.id}_${templateData.id}_${timestamp}_${randomSuffix}`,
          checklist_id: checklist.id,
          category_id: categoryId,
          description: description,
          completed: false,
          is_header: false,
          position: maxPosition + 1,
          template_id: templateData.id,
          created_at: now,
          updated_at: now
        }));

        const { error: itemsError } = await supabase
          .from('checklist_items')
          .insert(checklistItems);

        if (itemsError) {
          console.error('Error creating checklist items:', itemsError);
          throw itemsError;
        }
      }

      setTemplates(prev => prev.map(t =>
        t.type === templateType
          ? { ...t, items: [...t.items, templateData] }
          : t
      ));

      toast({
        title: 'Success',
        description: 'Task added successfully.',
      });
    } catch (error) {
      console.error('Error adding item:', error);
      toast({
        title: 'Error',
        description: 'Failed to add item. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteItem = async (itemId: string) => {
    if (deletingItems.has(itemId)) return;

    const templateInfo = templates.find(t =>
      t.items.some(item => item.id === itemId)
    );

    if (!templateInfo) return;

    const templateType = templateInfo.type;
    const deletedItem = templateInfo.items.find(item => item.id === itemId);

    if (!deletedItem) return;

    setDeletingItems(prev => new Set([...prev, itemId]));
    deletionRef.current = {
      itemId,
      templateType,
      inProgress: true
    };

    const templateItems = [...templateInfo.items];

    try {
      setTemplates(prev => prev.map(t => ({
        ...t,
        items: t.items.filter(item => item.id !== itemId)
      })));

      const { error: itemsError } = await supabase
        .from('checklist_items')
        .delete()
        .eq('template_id', itemId);

      if (itemsError) throw itemsError;

      const { error: templateError } = await supabase
        .from('checklist_templates')
        .delete()
        .eq('id', itemId);

      if (templateError) throw templateError;

      if (!unmountedRef.current && deletionRef.current?.itemId === itemId) {
        setDeletingItems(prev => {
          const next = new Set(prev);
          next.delete(itemId);
          return next;
        });
        deletionRef.current = null;
      }
    } catch (error) {
      console.error('Error deleting item:', error);

      if (!unmountedRef.current && deletionRef.current?.itemId === itemId) {
        setTemplates(prev => prev.map(t =>
          t.type === templateType
            ? { ...t, items: templateItems }
            : t
        ));

        setDeletingItems(prev => {
          const next = new Set(prev);
          next.delete(itemId);
          return next;
        });
        deletionRef.current = null;

        toast({
          title: 'Error',
          description: 'Failed to delete item. Please try again.',
          variant: 'destructive',
        });
      }
    }
  };

  const handleEditItem = async (itemId: string, newDescription: string) => {
    const templateInfo = templates.find(t =>
      t.items.some(item => item.id === itemId)
    );

    if (!templateInfo) {
      toast({ title: 'Error', description: 'Could not find the associated template.', variant: 'destructive' });
      return;
    }

    const templateType = templateInfo.type;
    const originalItem = templateInfo.items.find(item => item.id === itemId);

    if (!originalItem) {
        toast({ title: 'Error', description: 'Could not find the item to edit.', variant: 'destructive' });
        return;
    }

    const originalTemplatesState = JSON.parse(JSON.stringify(templates));

    setTemplates(prev => prev.map(t => {
      if (t.type === templateType) {
        return {
          ...t,
          items: t.items.map(item =>
            item.id === itemId
              ? { ...item, description: newDescription }
              : item
          )
        };
      }
      return t;
    }));

    try {
      const { error: templateUpdateError } = await supabase
        .from('checklist_templates')
        .update({ description: newDescription, updated_at: new Date().toISOString() })
        .eq('id', itemId);

      if (templateUpdateError) {
        console.error('Error updating checklist_templates:', templateUpdateError);
        throw new Error(`Failed to update template: ${templateUpdateError.message}`);
      }

      console.log(`Calling propagate_template_item_update for item ${itemId} with description "${newDescription}" and type ${templateType}`);
      const { error: propagateError } = await supabase
        .rpc('propagate_template_item_update', {
          p_new_description: newDescription,
          p_template_id: itemId,
          p_template_type: templateType
        });

      if (propagateError) {
        console.error('Error propagating changes via RPC:', propagateError);
        throw new Error(`Failed to propagate changes to existing checklists: ${propagateError.message}`);
      }

      console.log(`[ChecklistManagementPage] RPC propagate_template_item_update successful for item: ${itemId}`);
      toast({
        title: 'Success',
        description: 'Task updated successfully and changes propagated.',
      });

    } catch (error: any) {
      console.error('Error during handleEditItem:', error);

      setTemplates(originalTemplatesState);

      toast({
        title: 'Error Updating Task',
        description: error.message || 'Failed to update task. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleRenameCategory = async (categoryId: string, newName: string) => {
    if (isProcessing) return;
    setIsProcessing(true);

    const originalCategory = categories.find(c => c.id === categoryId);
    if (!originalCategory) {
        toast({ title: 'Error', description: 'Category not found.', variant: 'destructive' });
        setIsProcessing(false);
        return;
    }
    const originalTemplates = JSON.parse(JSON.stringify(templates));

    setCategories(prev => prev.map(c => c.id === categoryId ? { ...c, name: newName } : c));
    setTemplates(prev => prev.map(template => ({
        ...template,
        items: template.items.map(item =>
            item.category_id === categoryId && item.description === originalCategory.name
                ? { ...item, description: newName }
                : item
        )
    })));

    try {
        const { error: categoryError } = await supabase
            .from('checklist_categories')
            .update({ name: newName, updated_at: new Date().toISOString() })
            .eq('id', categoryId);

        if (categoryError) throw categoryError;

        const { error: templateError } = await supabase
            .from('checklist_templates')
            .update({ description: newName, updated_at: new Date().toISOString() })
            .eq('category_id', categoryId)
            .eq('description', originalCategory.name);

        if (templateError) {
            console.warn('Template header item update failed (might not exist or name mismatch):', templateError.message);
        }

        const { error: itemsError } = await supabase.rpc('update_checklist_item_headers', {
          p_category_id: categoryId,
          p_new_name: newName,
        });

        if (itemsError) {
          console.error('Error updating checklist item headers:', itemsError);
          throw new Error(`Failed to update checklist item headers: ${itemsError.message}`);
        }

        toast({
            title: 'Success',
            description: `Category renamed to "${newName}" successfully.`,
        });

    } catch (error: any) {
        console.error('Error renaming category:', error);

        setCategories(prev => prev.map(c => c.id === categoryId ? originalCategory : c));
        setTemplates(originalTemplates);

        toast({
            title: 'Error Renaming Category',
            description: error.message || 'Failed to rename category. Please try again.',
            variant: 'destructive',
        });
    } finally {
        setIsProcessing(false);
    }
  };

  const handleCreateChecklist = async (name: string) => {
    if (isProcessing) return;
    setIsProcessing(true);

    try {
      const type = name.toLowerCase().replace(/\s+/g, '-');

      if (templates.some(t => t.type === type)) {
        toast({
          title: 'Duplicate Checklist Type',
          description: `A checklist template with the type '${type}' already exists.`,
        });
        setIsProcessing(false);
        return;
      }

      const newTemplate: ChecklistTemplate = {
        type: type,
        items: []
      };

      setTemplates(prev => [...prev, newTemplate]);

      if (emblaApi) {
        setTimeout(() => emblaApi.scrollTo(templates.length), 100);
      }

      toast({
        description: `Empty checklist template '${name}' created. Add categories and tasks.`,
      });
    } catch (error: any) {
      console.error('Error creating checklist:', error);
      toast({
        title: 'Error Creating Checklist',
        description: error.message || 'Failed to create checklist template. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDeleteChecklist = (type: string) => {
    setChecklistToDelete(type);
    setDeleteChecklistModalOpen(true);
  };

  const confirmDeleteChecklist = async () => {
    if (!checklistToDelete || isProcessing) return;
    setIsProcessing(true);

    try {
      const templateToDelete = templates.find(t => t.type === checklistToDelete);
      if (!templateToDelete) throw new Error('Checklist not found');

      const templateItemIds = templateToDelete.items.map(item => item.id);

      if (templateItemIds.length > 0) {
        const { error: itemsError } = await supabase
          .from('checklist_items')
          .delete()
          .in('template_id', templateItemIds);

        if (itemsError) throw itemsError;

        const { error: templatesError } = await supabase
          .from('checklist_templates')
          .delete()
          .eq('type', checklistToDelete);

        if (templatesError) throw templatesError;
      }

      setTemplates(prev => prev.filter(t => t.type !== checklistToDelete));

      const checklistName = checklistToDelete
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');

      toast({
        title: 'Success',
        description: `${checklistName} checklist deleted successfully.`,
      });
    } catch (error) {
      console.error('Error deleting checklist:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete checklist. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setDeleteChecklistModalOpen(false);
      setChecklistToDelete(null);
      setIsProcessing(false);
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Checklist Templates Management</h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            className="bg-white text-blue-600 hover:bg-blue-50"
            onClick={() => setCreateChecklistModalOpen(true)}
          >
            <Plus className="h-4 w-4 mr-1" />
            Create Checklist
          </Button>
          <Button variant="outline" onClick={() => navigate(-1)}>Back</Button>
        </div>
      </div>

      <div className="relative bg-white/50 backdrop-blur-sm rounded-xl p-6 shadow-sm border border-gray-100">
        <div className="overflow-hidden rounded-lg" ref={emblaRef}>
          <div className="flex">
            {templates.map((template) => (
              <ChecklistPanel
                key={template.type}
                type={template.type}
                template={template}
                templates={templates}
                categories={categories}
                onDelete={handleDeleteItem}
                onEdit={handleEditItem}
                onAddItem={handleAddItem}
                onDragEnd={handleDragEnd}
                deletingItems={deletingItems}
                setCategories={setCategories}
                setTemplates={setTemplates}
                onDeleteChecklist={handleDeleteChecklist}
                onRenameCategory={handleRenameCategory}
              />
            ))}
          </div>
        </div>

        {/* Carousel Navigation and Indicators */}
        <div className="flex items-center justify-center gap-6 mt-6">
          <button
            className="bg-blue-600 hover:bg-blue-700 text-white rounded-lg px-6 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
            onClick={() => emblaApi?.scrollPrev()}
          >
            <ChevronLeft className="h-5 w-5" />
          </button>

          <div className="flex gap-3">
            {['CVCS', 'Advanced Staging', 'Technicians', 'UAT'].map((name, index) => (
              <button
                key={name}
                className={cn(
                  "w-2 h-2 rounded-full transition-all",
                  selectedChecklistIndex === index
                    ? "bg-blue-600 w-6"
                    : "bg-gray-300 hover:bg-gray-400"
                )}
                onClick={() => emblaApi?.scrollTo(index)}
              />
            ))}
          </div>

          <button
            className="bg-blue-600 hover:bg-blue-700 text-white rounded-lg px-6 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
            onClick={() => emblaApi?.scrollNext()}
          >
            <ChevronRight className="h-5 w-5" />
          </button>
        </div>
      </div>

      <CreateChecklistModal
        isOpen={createChecklistModalOpen}
        onClose={() => setCreateChecklistModalOpen(false)}
        onSave={handleCreateChecklist}
      />

      <DeleteChecklistModal
        isOpen={deleteChecklistModalOpen}
        onClose={() => {
          setDeleteChecklistModalOpen(false);
          setChecklistToDelete(null);
        }}
        onConfirm={confirmDeleteChecklist}
        checklistType={checklistToDelete || ''}
      />
    </div>
  );
}