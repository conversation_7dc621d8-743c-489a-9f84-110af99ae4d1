import React, { useEffect, useState, ChangeEvent } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { useUser } from '@/contexts/UserContext';
import { LoadingAnimation } from '@/components/LoadingAnimation';
import { usePageLoading } from '@/hooks/usePageLoading';

// LoginPage component handles user authentication and login functionality
const LoginPage = () => {
  const navigate = useNavigate();
  const { login, register } = useUser();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false);
  const [isRegistering, setIsRegistering] = useState(false);
  const [registrationKey, setRegistrationKey] = useState('');
  const [error, setError] = useState('');
  const [showWelcome, setShowWelcome] = useState(false);
  const [isExiting, setIsExiting] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const { loading, fadeOut } = usePageLoading(isInitialLoading);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const success = await login(username, password);
      if (success) {
        setShowWelcome(true);
        // Start fade out after 3 seconds
        setTimeout(() => {
          setIsExiting(true);
        }, 3000);
        // Navigate after fade out animation
        setTimeout(() => {
          navigate('/');
        }, 3500); // 3.5 seconds total (3s display + 0.5s fade out)
      } else {
        setError('Invalid credentials');
      }
    } catch (err) {
      setError('An error occurred during login');
    }
  };

  const handleRegisterAttempt = () => {
    if (registrationKey === 'bicpro') {
      setIsRegisterModalOpen(false);
      setIsRegistering(true);
      setUsername('');
      setPassword('');
      setError('');
    } else {
      setError('Invalid registration key');
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const success = await register(username, password);
      if (success) {
        navigate('/');
      } else {
        setError('Registration failed');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred during registration');
    }
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>, setter: (value: string) => void) => {
    setter(e.target.value);
  };

  const BackgroundLogo = () => (
    <div className="absolute inset-0 w-full h-full">
      <div
        className="absolute inset-0 opacity-10 bg-no-repeat bg-center"
        style={{
          backgroundSize: '100%',
          filter: 'grayscale(100%) brightness(0.5)',
          transform: 'scale(1.3)',
        }}
      />
    </div>
  );

  useEffect(() => {
    setIsInitialLoading(false);
  }, []);

  if (loading) {
    return <LoadingAnimation fadeOut={fadeOut} />;
  }

  if (showWelcome) {
    return (
      <motion.div 
        initial={{ opacity: 1 }}
        animate={{ opacity: isExiting ? 0 : 1 }}
        transition={{ duration: 0.5 }}
        className="min-h-screen w-full bg-black flex items-center justify-center p-4 relative overflow-hidden"
      >
        <BackgroundLogo />
        <div className="relative z-10 flex items-center justify-center">
          <div className="flex flex-wrap justify-center gap-x-2 text-3xl md:text-4xl lg:text-5xl font-bold">
            <motion.span
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="text-white"
            >
              WELCOME
            </motion.span>
            <motion.span
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="text-blue-400"
            >
              {username}
            </motion.span>
            <motion.span
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.6 }}
              className="text-white"
            >
              TO
            </motion.span>
            <motion.span
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.8 }}
              className="text-white"
            >
              THE
            </motion.span>
            <motion.span
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 1.0 }}
              className="text-white"
            >
              BEINCOURT
            </motion.span>
            <motion.span
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 1.2 }}
              className="text-white"
            >
              DASHBOARD
            </motion.span>
          </div>
        </div>
      </motion.div>
    );
  }

  if (isRegisterModalOpen) {
    return (
      <div className="min-h-screen w-full bg-black flex items-center justify-center p-4 relative overflow-hidden">
        <div className="absolute inset-0 w-full h-full">
          <div
            className="absolute inset-0 opacity-10 bg-no-repeat bg-center"
            style={{
              backgroundSize: '100%',
              filter: 'grayscale(100%) brightness(0.5)',
              transform: 'scale(1.3)',
            }}
          />
        </div>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md relative z-10"
        >
          <div className="bg-white/5 backdrop-blur-lg rounded-lg p-8 shadow-xl border border-white/10">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">Registration Key Required</h2>
              <p className="text-gray-400">
                Please enter the registration key to continue
              </p>
            </div>
            <div className="space-y-4 mt-6">
              <Input
                type="text"
                placeholder="Enter registration key"
                value={registrationKey}
                onChange={(e) => handleInputChange(e, setRegistrationKey)}
                className="bg-white/10 text-white placeholder:text-gray-400 border-white/10"
              />
              {error && <p className="text-red-400 text-sm">{error}</p>}
              <div className="space-y-2">
                <Button
                  onClick={handleRegisterAttempt}
                  className="w-full bg-white text-purple-950 hover:bg-gray-100"
                >
                  Submit
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  className="w-full text-white hover:bg-white/10"
                  onClick={() => setIsRegisterModalOpen(false)}
                >
                  Back to Login
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen w-full bg-black flex items-center justify-center p-4 relative overflow-hidden">
      <div className="absolute inset-0 w-full h-full">
        <div
          className="absolute inset-0 opacity-10 bg-no-repeat bg-center"
          style={{
            backgroundSize: '100%',
            filter: 'grayscale(100%) brightness(0.5)',
            transform: 'scale(1.3)',
          }}
        />
      </div>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md relative z-10"
      >
        <div className="bg-white/5 backdrop-blur-lg rounded-lg p-8 shadow-xl border border-white/10">
          <h2 className="text-3xl font-bold text-white mb-6 text-center">BEINCOURT DASHBOARD</h2>
          <form onSubmit={isRegistering ? handleRegister : handleLogin} className="space-y-4">
            {error && (
              <div className="bg-red-500/10 border border-red-500/50 text-red-300 px-4 py-2 rounded">
                {error}
              </div>
            )}
            <div>
              <Input
                type="text"
                placeholder="Username"
                value={username}
                onChange={(e) => handleInputChange(e, setUsername)}
                className="w-full bg-white/10 text-white placeholder:text-gray-400 border-white/10"
              />
            </div>
            <div>
              <Input
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => handleInputChange(e, setPassword)}
                className="w-full bg-white/10 text-white placeholder:text-gray-400 border-white/10"
              />
            </div>
            <div className="space-y-2">
              <Button
                type="submit"
                className="w-full bg-white text-purple-950 hover:bg-gray-100 transition-colors"
              >
                {isRegistering ? 'Register' : 'Login'}
              </Button>
              {!isRegistering ? (
                <Button
                  type="button"
                  variant="ghost"
                  className="w-full text-white hover:bg-white/10"
                  onClick={() => setIsRegisterModalOpen(true)}
                >
                  Register
                </Button>
              ) : (
                <Button
                  type="button"
                  variant="ghost"
                  className="w-full text-white hover:bg-white/10"
                  onClick={() => setIsRegistering(false)}
                >
                  Back to Login
                </Button>
              )}
            </div>
          </form>
        </div>
      </motion.div>
    </div>
  );
};

export default LoginPage;