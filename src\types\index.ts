// Core type definitions for the dashboard.

// Represents a department within a courthouse
export interface Department {
  id: string;
  courthouse_id: string;
  name: string;
  status: ProjectStatus;
  progress: number;
  floor?: number | null;
  reference_number?: number | null;
  has_technicians_checklist: boolean;
  has_uat_checklist: boolean;
  has_cvcs_checklist?: boolean | null;
  has_advanced_staging_checklist?: boolean | null;
  has_rack_preparation_checklist?: boolean | null;
  has_engineering_checklist?: boolean | null;
  created_at: string;
  updated_at: string;

  checklists?: Array<{
    id: string;
    type: ChecklistType;
    checklist_items: Array<{ id: string; completed: boolean; }>;
  }>;
  issues?: Array<{
    id: string;
    title: string;
    content: string;
    status: 'Open' | 'In Progress' | 'Resolved';
    created_by_username: string;
    created_at: string;
    task_id?: string;
    task_description?: string;
    checklist_type?: string;
    category_name?: string;
    department_id: string;
  }>;
}

// Represents an installation or configuration task
export interface Task {
  id: string;
  description: string;
  completed: boolean;
  notes: TaskNote[];
}

// A note attached to a specific task
export interface TaskNote {
  id: string;
  taskId: string;
  userId: string;
  username: string;
  content: string;
  timestamp: string;
}

// A general note attached to a department
export interface GeneralNote {
  id: string;
  departmentId: string;
  userId: string;
  username: string;
  content: string;
  timestamp: string;
}

// An item within a checklist category
export interface ChecklistSubtask {
  id: string;
  description: string;
  completed: boolean;
  notes: TaskNote[];
  completedBy: CompletedBy | null;
}

// Records who completed a task and when
export interface CompletedBy {
  userId: string;
  username: string;
  timestamp: string;
}

// A category of related checklist subtasks
export interface ChecklistCategory {
  id: string;
  name: string;
  subtasks: ChecklistSubtask[];
}

// The complete checklist for a department
export interface Checklist {
  id: string;
  departmentId: string;
  type: ChecklistType;
  categories: ChecklistCategory[];
}

// Possible states for projects, courthouses, or departments
export type ProjectStatus = 'on-track' | 'delayed' | 'complete';

// Possible types of checklists
export type ChecklistType = 
  | 'technicians' 
  | 'uat' 
  | 'cvcs' 
  | 'advanced-staging'
  | 'rack-preparation'
  | 'engineering';

// Represents a single item in a checklist (consider merging with Task/Subtask?)
export interface ChecklistItem {
  id: string;
  description: string;
  completed: boolean;
  notes?: string;
  completed_by_username?: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
}