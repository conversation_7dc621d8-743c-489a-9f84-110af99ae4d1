import { cn } from '@/lib/utils';

interface LoadingAnimationProps {
  fadeOut?: boolean;
}

export const LoadingAnimation = ({ fadeOut }: LoadingAnimationProps) => {
  return (
    <div className={cn(
      "fixed inset-0 bg-gray-100 flex items-center justify-center min-h-screen z-50",
      fadeOut && "animate-fade-out"
    )}>
      <div className="loader">
        <style>{`
          .loader {
            width: fit-content;
            font-size: 40px;
            font-family: system-ui, sans-serif;
            font-weight: bold;
            text-transform: uppercase;
            color: #0000;
            -webkit-text-stroke: 1px #2563eb;
            background: 
              linear-gradient(90deg, #0000 33%, #2563eb 0 67%, #0000 0) 
              0/300% 100% no-repeat text;
            animation: l2 1.0s linear infinite;
          }
          .loader:before {
            content: "BEINCOURT";
          }
          @keyframes l2 {
            0% { background-position: 100% }
          }
          @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
          }
          @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
          }
          .animate-fade-in {
            animation: fadeIn 0.1s ease-in-out;
          }
          .animate-fade-out {
            animation: fadeOut 0.1s ease-in-out;
          }
        `}</style>
      </div>
    </div>
  );
}; 