import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from './ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from './ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from './ui/form';
import { Input } from './ui/input';
import { useToast } from './ui/use-toast';
import { createCourthouse } from '../lib/db';
import { Plus } from 'lucide-react';

const formSchema = z.object({
  name: z.string().min(1, 'Courthouse name is required'),
  address: z.string().min(1, 'Address is required'),
});

interface CreateCourthouseDialogProps {
  projectId: string;
  onCourthouseCreated?: () => void;
}

type FormValues = z.infer<typeof formSchema>;

export function CreateCourthouseDialog({ projectId, onCourthouseCreated }: CreateCourthouseDialogProps) {
  const [open, setOpen] = React.useState(false);
  const { toast } = useToast();
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      address: '',
    },
  });

  async function onSubmit(values: FormValues) {
    try {
      await createCourthouse({
        name: values.name,
        address: values.address,
        projectId,
      });

      setOpen(false);
      form.reset();
      onCourthouseCreated?.();

      toast({
        title: 'Success',
        description: 'Courthouse created successfully',
      });
    } catch (error) {
      console.error('Error creating courthouse:', error);
      toast({
        title: 'Error',
        description: 'Failed to create courthouse. Please try again.',
        variant: 'destructive',
      });
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Plus className="h-4 w-4" />
          New Courthouse
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New Courthouse</DialogTitle>
          <DialogDescription>
            Add a new courthouse to this project. Fill in the required information below.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter courthouse name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter courthouse address" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button type="submit" disabled={form.formState.isSubmitting}>
                {form.formState.isSubmitting ? 'Creating...' : 'Create Courthouse'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 
