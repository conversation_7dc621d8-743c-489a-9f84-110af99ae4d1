import { createClient } from '@supabase/supabase-js';

export type UserCompany = 'BEINCOURT' | 'CVCS' | 'JUDCO' | 'admin';

export type User = {
  id: string;
  username: string;
  created_at: string;
  company: UserCompany;
};

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

export const supabase = createClient(
  supabaseUrl || 'https://ozbjosdvfjfegkkwodmg.supabase.co',
  supabaseAnonKey || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im96Ympvc2R2ZmpmZWdra3dvZG1nIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzY0ODk4NDMsImV4cCI6MjA1MjA2NTg0M30.3cuMSWh0jPIpNfCenkrR9NkGlmiy2dXBMA0OVllO8Kg',
  {
  auth: {
    persistSession: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  },
  db: {
    schema: 'public'
  }
});

// Enable real-time subscriptions for specific tables
void supabase.channel('custom-all-channel')
  .on(
    'postgres_changes',
    { event: '*', schema: 'public', table: 'checklist_items' },
    (payload) => {
      console.log('Global checklist item change:', payload);
    }
  )
  .subscribe();