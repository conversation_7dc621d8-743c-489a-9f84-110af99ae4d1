// Displays details and checklists for a specific department.
import React, { useEffect, useState, useMemo, useRef } from 'react';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import { getDepartment, updateChecklistItem, updateDepartmentProgress, addChecklistItemNote, deleteChecklistItemNote, addIssue, updateIssue, deleteIssue } from '../lib/db';
import type { Database } from '../types/supabase';
import { useUser } from '@/contexts/UserContext';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { format } from 'date-fns';
import { Trash2, Pencil, CheckCircle2, Circle, Flag, ChevronRight, ChevronLeft, StickyNote, Settings } from 'lucide-react';
import { cn } from '@/lib/utils';
import useEmblaCarousel from 'embla-carousel-react';
import { LoadingAnimation } from '@/components/LoadingAnimation';
import { usePageLoading } from '@/hooks/usePageLoading';
import { toast } from '@/components/ui/use-toast';
import { supabase } from '../lib/supabase';

interface ChecklistNote {
  id: string;
  content: string;
  created_by_username: string;
  created_at: string;
}

interface ChecklistItemWithCategory {
  id: string;
  description: string;
  completed: boolean;
  notes: ChecklistNote[];
  completed_by_username?: string | null;
  completed_at?: string | null;
  created_at: string;
  updated_at: string;
  position: number | null;
  is_header?: boolean;
  checklist_categories: {
    id: string;
    name: string;
  };
}

interface DepartmentWithChecklists {
  id: string;
  courthouse_id: string;
  name: string;
  status: 'on-track' | 'delayed' | 'complete';
  progress: number;
  has_technicians_checklist: boolean;
  has_uat_checklist: boolean;
  has_cvcs_checklist: boolean;
  has_advanced_staging_checklist: boolean;
  checklists: Array<{
    id: string;
    type: 'technicians' | 'uat' | 'cvcs' | 'advanced-staging' | 'rack-preparation' | 'engineering';
    checklist_items: ChecklistItemWithCategory[];
  }>;
  issues?: Array<Issue>;
  reference_number?: string;
}

type IssueStatus = 'Open' | 'In Progress' | 'Resolved';

interface Issue {
  id: string;
  title: string;
  content: string;
  status: IssueStatus;
  created_by_username: string;
  created_at: string;
  task_id?: string;
  task_description?: string;
  checklist_type?: string;
  category_name?: string;
}

const getCategoryOrder = (items: ChecklistItemWithCategory[]): string[] => {
  // Get unique category IDs while preserving order of first appearance
  const uniqueCategories = Array.from(new Set(
    items.map(item => item.checklist_categories.id)
  ));
  return uniqueCategories;
};

// Gets the name from a category object.
const getCategoryName = (category: { id: string, name: string }): string => {
  return category.name;
};

const transformAndSortChecklistItems = (checklist: DepartmentWithChecklists['checklists'][0]) => {
  return checklist.checklist_items.sort((a, b) => {
    // First check completion status - completed items go to the bottom
    if (a.completed !== b.completed) {
      return a.completed ? 1 : -1;
    }

    // For items with the same completion status, sort by category order
    const categoryOrder = getCategoryOrder(checklist.checklist_items);
    const categoryOrderA = categoryOrder.indexOf(a.checklist_categories.id);
    const categoryOrderB = categoryOrder.indexOf(b.checklist_categories.id);
    if (categoryOrderA !== categoryOrderB) {
      return categoryOrderA - categoryOrderB;
    }

    // Then sort by position within the same category
    if (a.position !== null && b.position !== null) {
      return a.position - b.position;
    }

    // Fall back to alphabetical order
    return a.description.localeCompare(b.description);
  });
};

// Groups checklist items by their category ID.
const groupItemsByCategory = (items: Array<ChecklistItemWithCategory>) => {
  const grouped = new Map<string, Array<ChecklistItemWithCategory>>();

  items.forEach(item => {
    const categoryId = item.checklist_categories.id;
    if (!grouped.has(categoryId)) {
      grouped.set(categoryId, []);
    }
    grouped.get(categoryId)?.push(item);
  });

  return grouped;
};

// Counts open issues (flags) associated with a specific task ID.
const getTaskFlagCount = (taskId: string, issues: Issue[] = []): number => {
  return issues.filter(issue => issue.task_id === taskId).length;
};

// Formats a checklist type string (e.g., 'advanced-staging') for display.
const formatChecklistName = (type: string): string => {
  // Handle known checklist types explicitly if needed
  if (type === 'uat') return 'UAT';
  if (type === 'cvcs') return 'CVCS';
  if (type === 'advanced-staging') return 'Advanced Staging';
  if (type === 'rack-preparation') return 'Rack Preparation';
  if (type === 'engineering') return 'Engineering';

  // Format custom/other types: replace dashes, capitalize words
  return type
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// Reusable Checklist Component
const ChecklistPanel = ({
  type,
  checklist,
  onToggle,
  calculateProgress,
  updatingItems,
  onFlagTask,
  highlightedTaskId,
  taskRefs,
  issues,
  setEditingNoteItem
}: {
  type: 'technicians' | 'uat' | 'cvcs' | 'advanced-staging' | 'rack-preparation' | 'engineering';
  checklist: DepartmentWithChecklists['checklists'][0];
  onToggle: (id: string) => void;
  calculateProgress: (type: 'technicians' | 'uat' | 'cvcs' | 'advanced-staging' | 'rack-preparation' | 'engineering') => number;
  updatingItems: Set<string>;
  onFlagTask: (task: ChecklistItemWithCategory, type: string, categoryName?: string) => void;
  highlightedTaskId: string | null;
  taskRefs: React.MutableRefObject<Map<string, HTMLDivElement>>;
  issues: Issue[];
  setEditingNoteItem: (item: ChecklistItemWithCategory | null) => void;
}) => {
  const progress = calculateProgress(type);
  const title = formatChecklistName(type);

  // Calculate latest completion time if progress is 100%
  const latestCompletionTime = useMemo(() => {
    if (progress === 100) {
      const completedItems = checklist.checklist_items.filter(item => item.completed && item.completed_at);
      if (completedItems.length > 0) {
        const latestTimestamp = Math.max(...completedItems.map(item => new Date(item.completed_at!).getTime()));
        // Format the date, handle potential invalid date
        try {
          return format(new Date(latestTimestamp), 'PPpp'); // e.g., Jul 21, 2024, 3:30:00 PM
        } catch (error) {
          return "Invalid Date"; // Fallback for invalid dates
        }
      }
    }
    return null; // Return null if not 100% complete or no completed items with timestamps
  }, [checklist.checklist_items, progress]);

  const items = transformAndSortChecklistItems(checklist);
  const groupedItems = groupItemsByCategory(items);

  useEffect(() => {
  }, [items, groupedItems, title]);

  return (
    <div className="flex-[0_0_100%] sm:flex-[0_0_45%] min-w-0 px-2">
      <div className="bg-white rounded-lg shadow-sm flex flex-col h-[calc(100vh-20rem)] sm:h-[calc(100vh-20rem)]">
        <div className="border-b px-4 sm:px-6 py-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-base sm:text-lg font-semibold text-gray-800">
              {title}
            </h3>
            <span className="text-xs sm:text-sm font-medium text-gray-600">
              {progress === 100 && latestCompletionTime
                ? `Completed: ${latestCompletionTime}`
                : `${progress}% Complete`}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-1.5">
            <div
              className={cn(
                "rounded-full h-1.5 transition-all duration-300",
                progress === 100 ? "bg-green-500" :
                progress >= 75 ? "bg-yellow-500" :
                progress >= 40 ? "bg-orange-500" : "bg-red-500"
              )}
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>
        <div className="flex-1 overflow-y-auto">
          <div className="divide-y">
            {getCategoryOrder(items).map(categoryId => {
              const categoryItems = groupedItems.get(categoryId) || [];
              if (categoryItems.length === 0) return null;
              const category = categoryItems[0].checklist_categories;

              return (
                <div key={categoryId} className="divide-y">
                  <div className="px-4 sm:px-6 py-2 bg-gray-50">
                    <h4 className="text-sm font-medium text-gray-700 text-center">
                      {getCategoryName(category)}
                    </h4>
                  </div>
                  {categoryItems
                    // Filter out the items that represent the category header itself
                    .filter(item => !item.is_header)
                    .map((item) => {
                      const flagCount = getTaskFlagCount(item.id, issues);
                      return (
                        <div
                          key={item.id}
                          ref={(el) => {
                            if (el) {
                              taskRefs.current.set(item.id, el);
                            } else {
                              taskRefs.current.delete(item.id);
                            }
                          }}
                        >
                          <div
                            className={cn(
                              "px-4 sm:px-6 py-3 flex items-center gap-2 sm:gap-4 group transition-all duration-300",
                              item.completed && "bg-gray-50",
                              highlightedTaskId === item.id && "bg-blue-100",
                              flagCount > 0 && "bg-red-50"
                            )}
                          >
                            <div
                              className="flex items-center flex-1 gap-4"
                              onClick={() => onToggle(item.id)}
                            >
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onToggle(item.id);
                                }}
                                className="flex-shrink-0"
                              >
                                {item.completed ? (
                                  <CheckCircle2 className={cn(
                                    "h-5 w-5",
                                    updatingItems.has(item.id)
                                      ? "text-blue-400 animate-pulse"
                                      : "text-blue-600"
                                  )} />
                                ) : (
                                  <Circle className={cn(
                                    "h-5 w-5",
                                    updatingItems.has(item.id)
                                      ? "text-gray-300 animate-pulse"
                                      : "text-gray-400 group-hover:text-blue-600"
                                  )} />
                                )}
                              </button>
                              <div className="flex-1 min-w-0">
                                <p className={cn(
                                  "text-gray-900",
                                  item.completed && "line-through text-gray-500"
                                )}>
                                  {item.description}
                                </p>
                                {item.completed && item.completed_by_username && (
                                  <p className="text-sm text-gray-500">
                                    Completed by {item.completed_by_username}
                                    {item.completed_at && ` on ${format(new Date(item.completed_at), 'MMM d, yyyy')}`}
                                  </p>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <button
                                onClick={() => onFlagTask(item, type)}
                                className={cn(
                                  "flex items-center gap-1.5 text-sm transition-colors",
                                  flagCount > 0 ? "text-red-500 hover:text-red-600" : "text-gray-500 hover:text-red-600",
                                  "hover:scale-105 active:scale-95"
                                )}
                              >
                                <Flag className="h-4 w-4" />
                                {flagCount > 0 && (
                                  <span className="text-xs font-medium">{flagCount}</span>
                                )}
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setEditingNoteItem(item);
                                }}
                                className={cn(
                                  "flex items-center gap-1.5 text-sm transition-colors",
                                  item.notes?.length > 0 ? "text-green-500 hover:text-green-600" : "text-gray-500 hover:text-green-600"
                                )}
                              >
                                <StickyNote className="h-4 w-4" />
                                {item.notes?.length > 0 && (
                                  <span className="text-xs font-medium">{item.notes.length}</span>
                                )}
                              </button>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

// Add these new components before the DepartmentDetailsPage component
const IssueStatusBadge = ({ status }: { status: IssueStatus }) => {
  const colors = {
    'Open': 'bg-blue-100 text-blue-800 border-blue-200',
    'In Progress': 'bg-purple-100 text-purple-800 border-purple-200',
    'Resolved': 'bg-gray-100 text-gray-800 border-gray-200',
  };

  return (
    <span className={cn(
      'px-2 py-1 text-xs font-medium rounded-full border',
      colors[status]
    )}>
      {status}
    </span>
  );
};

// Add this helper function to check if a user has access to a checklist type
const hasChecklistAccess = (company: string, type: string): boolean => {
  if (company === 'admin') return true;
  if (company === 'CVCS') return type === 'cvcs';
  return company === 'BEINCOURT';
};

export function DepartmentDetailsPage() {
  const { departmentId } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const [department, setDepartment] = useState<DepartmentWithChecklists | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { loading: pageLoading, fadeOut } = usePageLoading(isLoading);
  const { user } = useUser();
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: 'start',
    containScroll: 'trimSnaps',
    dragFree: true,
    slidesToScroll: 1,
    skipSnaps: false
  });
  const [selectedChecklistIndex, setSelectedChecklistIndex] = useState(0);
  const [isAddingIssue, setIsAddingIssue] = useState(false);
  const [editingIssueId, setEditingIssueId] = useState<string | null>(null);
  const [editIssue, setEditIssue] = useState<Issue | null>(null);
  const [statusFilter, setStatusFilter] = useState<IssueStatus | 'All'>('All');
  const [updatingItems, setUpdatingItems] = useState<Set<string>>(new Set());
  const [flaggedTaskContext, setFlaggedTaskContext] = useState<{
    task: ChecklistItemWithCategory;
    type: string;
    categoryName?: string;
  } | null>(null);
  const [highlightedTaskId, setHighlightedTaskId] = useState<string | null>(null);
  const taskRefs = useRef<Map<string, HTMLDivElement>>(new Map());
  const [editingNoteItem, setEditingNoteItem] = useState<ChecklistItemWithCategory | null>(null);
  const [newNote, setNewNote] = useState('');

  // Initial data load
  useEffect(() => {
    async function initialLoad() {
      if (!departmentId) {
        setError('No department ID provided');
        setIsLoading(false);
        return;
      }

      try {
        const data = await getDepartment(departmentId);
        setDepartment(data);
      } catch (error) {
        setError('Failed to load department data');
      } finally {
        setIsLoading(false);
      }
    }

    void initialLoad();
  }, [departmentId]);

  // Add real-time subscriptions
  useEffect(() => {
    if (!departmentId) return;

    // Create a function to refresh data
    const refreshData = async () => {
      try {
        const data = await getDepartment(departmentId);
        setDepartment(data);
      } catch (error) {
      }
    };

    // Set up checklist items channel
    const channel = supabase.channel(`department-${departmentId}`, {
      config: {
        broadcast: { self: true },
        presence: { key: 'department-presence' },
      },
    });

    // Subscribe to checklist items changes
    channel
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'checklist_items'
        },
        (payload) => {
          console.log('[DepartmentDetailsPage] Realtime: Checklist item change detected:', payload);
          void refreshData();
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'checklist_item_notes'
        },
        (payload) => {
          void refreshData();
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'department_issues',
          filter: `department_id=eq.${departmentId}`
        },
        (payload) => {
          void refreshData();
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'checklist_categories'
        },
        (payload) => {
          void refreshData();
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'checklist_templates'
        },
        (payload) => {
          void refreshData();
        }
      )
      .on('presence', { event: 'sync' }, () => {
        // console.log('Presence state:', channel.presenceState());
      })
      .on('presence', { event: 'join' }, ({ /*key, newPresences*/ }) => {
        // console.log('Join:', key, newPresences);
      })
      .on('presence', { event: 'leave' }, ({ /*key, leftPresences*/ }) => {
        // console.log('Leave:', key, leftPresences);
      })
      .on('system', { event: 'disconnect' }, () => {
        // console.log('Disconnected from realtime');
      })
      .on('system', { event: 'reconnected' }, () => {
        void refreshData(); // Refresh data after reconnection
      });

    // Subscribe to the channel
    channel.subscribe(async (status) => {
      if (status === 'SUBSCRIBED') {
        // Track presence
        const presenceTrackStatus = await channel.track({
          user: departmentId,
          online_at: new Date().toISOString(),
        });

        // Log subscription state
        const state = supabase.getChannels();
      }
    });

    // Cleanup function
    return () => {
      void supabase.removeChannel(channel);
    };
  }, [departmentId]);

  useEffect(() => {
    if (emblaApi) {
      emblaApi.on('select', () => {
        setSelectedChecklistIndex(emblaApi.selectedScrollSnap());
      });
    }
  }, [emblaApi]);

  const handleChecklistItemToggle = async (itemId: string) => {
    if (!department || !user?.username) return;

    // Find the item
    const item = department.checklists
      .flatMap(cl => cl.checklist_items)
      .find(item => item.id === itemId);

    if (!item) return;

    const newCompleted = !item.completed;

    // Check if user is trying to uncheck a task completed by someone else
    if (item.completed && item.completed_by_username && item.completed_by_username !== user.username) {
      toast({
        title: "Action not allowed",
        description: `This task was completed by ${item.completed_by_username}. Only they can uncheck it.`,
        variant: "destructive"
      });
      return;
    }

    // Add item to updating set
    setUpdatingItems(prev => new Set(prev).add(itemId));

    // Optimistically update the UI immediately
    setDepartment(prev => {
      if (!prev) return null;

      return {
        ...prev,
        checklists: prev.checklists.map(checklist => ({
          ...checklist,
          checklist_items: checklist.checklist_items.map(item =>
            item.id === itemId ? {
              ...item,
              completed: newCompleted,
              completed_by_username: newCompleted ? user.username : null,
              completed_at: newCompleted ? new Date().toISOString() : null
            } : item
          )
        }))
      };
    });

    try {
      // Make the API call to update the backend
      await updateChecklistItem(itemId, newCompleted, user.username);

      // Calculate new progress
      const allItems = department.checklists.flatMap(cl => cl.checklist_items);
      const completedItems = allItems.filter(item =>
        item.id === itemId ? newCompleted : item.completed
      ).length;
      const progress = Math.round((completedItems / allItems.length) * 100);

      // Update department progress
      await updateDepartmentProgress(department.id, progress);

      // Update local progress state
      setDepartment(prev => {
        if (!prev) return null;
        return {
          ...prev,
          progress
        };
      });
    } catch (err) {
      // Revert the optimistic update if the backend call fails
      setDepartment(prev => {
        if (!prev) return null;

        return {
          ...prev,
          checklists: prev.checklists.map(checklist => ({
            ...checklist,
            checklist_items: checklist.checklist_items.map(item =>
              item.id === itemId ? {
                ...item,
                completed: !newCompleted,
                completed_by_username: !newCompleted ? user.username : null,
                completed_at: !newCompleted ? new Date().toISOString() : null
              } : item
            )
          }))
        };
      });
    } finally {
      // Remove item from updating set
      setUpdatingItems(prev => {
        const next = new Set(prev);
        next.delete(itemId);
        return next;
      });
    }
  };

  const handleAddNote = async (itemId: string, content: string) => {
    if (!department || !user?.username) return;

    try {
      const note = await addChecklistItemNote(itemId, content, user.username);

      // Update local state immediately
      setDepartment(prev => {
        if (!prev) return null;

        const updatedDepartment = {
          ...prev,
          checklists: prev.checklists.map(checklist => ({
            ...checklist,
            checklist_items: checklist.checklist_items.map(item =>
              item.id === itemId ? {
                ...item,
                notes: [...(item.notes || []), note]
              } : item
            )
          }))
        };

        // Update the editingNoteItem to reflect the new note
        const updatedItem = updatedDepartment.checklists
          .flatMap((c: DepartmentWithChecklists['checklists'][0]) => c.checklist_items)
          .find((item: ChecklistItemWithCategory) => item.id === itemId);

        if (updatedItem) {
          setEditingNoteItem(updatedItem);
        }

        return updatedDepartment;
      });

      // Clear the new note input but keep the dialog open
      setNewNote('');
    } catch (err) {
    }
  };

  const handleDeleteNote = async (noteId: string) => {
    if (!department || !editingNoteItem) return;

    try {
      // Update local state immediately before the API call
      setDepartment(prev => {
        if (!prev) return null;

        const updatedDepartment = {
          ...prev,
          checklists: prev.checklists.map(checklist => ({
            ...checklist,
            checklist_items: checklist.checklist_items.map(item => ({
              ...item,
              notes: item.notes.filter(note => note.id !== noteId)
            }))
          }))
        };

        // Update the editingNoteItem to reflect the deleted note
        const updatedItem = updatedDepartment.checklists
          .flatMap((c: DepartmentWithChecklists['checklists'][0]) => c.checklist_items)
          .find((item: ChecklistItemWithCategory) => item.id === editingNoteItem.id);

        if (updatedItem) {
          setEditingNoteItem(updatedItem);
        }

        return updatedDepartment;
      });

      // Make the API call after updating the UI
      await deleteChecklistItemNote(noteId);
    } catch (err) {
      // If the API call fails, we should revert the optimistic update
      // by refetching the department data
      if (departmentId) {
        const data = await getDepartment(departmentId);
        setDepartment(data);

        // Update the editingNoteItem with the reverted data
        const revertedItem = data?.checklists
          .flatMap((c: DepartmentWithChecklists['checklists'][0]) => c.checklist_items)
          .find((item: ChecklistItemWithCategory) => item.id === editingNoteItem.id);

        if (revertedItem) {
          setEditingNoteItem(revertedItem);
        }
      }
    }
  };

  const calculateChecklistProgress = (type: 'technicians' | 'uat' | 'cvcs' | 'advanced-staging' | 'rack-preparation' | 'engineering'): number => {
    if (!department?.checklists) return 0;

    const checklist = department.checklists.find(c => c.type === type);
    if (!checklist) return 0;

    // Get all items for the checklist
    const allItems = checklist.checklist_items;
    if (!allItems || allItems.length === 0) return 0;

    // Filter out header items before calculating progress
    const taskItems = allItems.filter(item => !item.is_header);
    if (taskItems.length === 0) return 100; // Or 0 if preferred when no tasks exist

    const completedTaskItems = taskItems.filter(item => item.completed).length;
    // Calculate progress based only on actual task items
    return Math.round((completedTaskItems / taskItems.length) * 100);
  };

  const scrollPrev = () => emblaApi?.scrollPrev();
  const scrollNext = () => emblaApi?.scrollNext();

  const handleFlagTask = async (task: ChecklistItemWithCategory, type: string, categoryName?: string) => {
    if (!department || !user?.username) return;

    // Always show the flag task dialog with task context
    setFlaggedTaskContext({ task, type, categoryName });
    setEditIssue({
      id: '',
      title: `Issue with task: ${task.description}`,
      content: '',
      status: 'Open',
      created_by_username: user.username,
      created_at: new Date().toISOString(),
      task_id: task.id,
      task_description: task.description,
      checklist_type: type === 'uat' ? 'UAT Checklist' :
                     type === 'cvcs' ? 'CVCS Checklist' :
                     type === 'advanced-staging' ? 'Advanced Staging Checklist' :
                     type === 'rack-preparation' ? 'Rack Preparation Checklist' :
                     type === 'engineering' ? 'Engineering Checklist' :
                     type.charAt(0).toUpperCase() + type.slice(1) + ' Checklist',
      category_name: categoryName
    });
    setIsAddingIssue(false);
    setEditingIssueId(null);
  };

  const handleAddIssue = async () => {
    if (!department || !user?.username || !editIssue) return;

    try {
      // Create issue data with task context
      const issueData = {
        department_id: department.id,
        title: editIssue.title,
        content: editIssue.content,
        status: editIssue.status as IssueStatus,
        created_by_username: user.username,
        task_id: flaggedTaskContext?.task.id,
        task_description: flaggedTaskContext?.task.description,
        checklist_type: flaggedTaskContext?.type === 'uat' ? 'UAT Checklist' :
                       flaggedTaskContext?.type === 'cvcs' ? 'CVCS Checklist' :
                       flaggedTaskContext?.type === 'advanced-staging' ? 'Advanced Staging Checklist' :
                       flaggedTaskContext?.type === 'rack-preparation' ? 'Rack Preparation Checklist' :
                       flaggedTaskContext?.type === 'engineering' ? 'Engineering Checklist' :
                       flaggedTaskContext?.type ? flaggedTaskContext.type.charAt(0).toUpperCase() + flaggedTaskContext.type.slice(1) + ' Checklist' : undefined,
        category_name: flaggedTaskContext?.categoryName,
        updated_at: new Date().toISOString()
      };

      const issue = await addIssue(department.id, issueData);

      // Update local state with the new issue
      setDepartment(prev => {
        if (!prev) return null;
        return {
          ...prev,
          issues: prev.issues ? [issue, ...prev.issues] : [issue]
        };
      });

      // Reset all issue-related state
      setIsAddingIssue(false);
      setEditingIssueId(null);
      setEditIssue(null);
      setFlaggedTaskContext(null);
    } catch (err) {
    }
  };

  const handleEditIssue = async () => {
    if (!department || !editingIssueId || !editIssue) return;

    try {
      const updatedIssue = await updateIssue(editingIssueId, {
        title: editIssue.title,
        content: editIssue.content,
        status: editIssue.status
      });

      setDepartment(prev => prev ? {
        ...prev,
        issues: prev.issues?.map(issue =>
          issue.id === editingIssueId ? updatedIssue : issue
        ) || []
      } : null);
      setIsAddingIssue(false);
      setEditingIssueId(null);
      setEditIssue(null);
    } catch (err) {
    }
  };

  const handleDeleteIssue = async (issueId: string) => {
    if (!department) return;

    try {
      await deleteIssue(issueId);
      setDepartment(prev => prev ? {
        ...prev,
        issues: prev.issues?.filter(issue => issue.id !== issueId) || []
      } : null);
    } catch (err) {
    }
  };

  const handleIssueClick = (taskId: string | undefined, checklistType: string | undefined) => {
    if (!taskId || !checklistType || !emblaApi || !department) return;

    // Convert checklist type string to type value
    const targetType = checklistType as typeof department.checklists[0]["type"];

    const targetChecklistIndex = department.checklists.findIndex(c => c.type === targetType);

    if (targetChecklistIndex === -1) {
      return;
    }

    // Find the index of the checklist to navigate to
    emblaApi.scrollTo(targetChecklistIndex);

    // Highlight the task
    setHighlightedTaskId(taskId);

    // Scroll the task into view after carousel potentially moves
    setTimeout(() => {
      const taskElement = taskRefs.current.get(taskId);
      taskElement?.scrollIntoView({ behavior: 'smooth', block: 'center' });

      // Remove highlight after animation
      setTimeout(() => setHighlightedTaskId(null), 1500); 
    }, 300); // Wait for carousel transition
  };

  // Handle task context from navigation state
  useEffect(() => {
    const state = location.state as { taskContext?: { taskId: string, checklistType: string } } | null;
    if (state?.taskContext?.taskId && state?.taskContext?.checklistType) {
      handleIssueClick(state.taskContext.taskId, state.taskContext.checklistType);
    }
  }, [location.state]);

  if (pageLoading) {
    return <LoadingAnimation fadeOut={fadeOut} />;
  }

  if (error) {
    return (
      <div className="flex-1 flex flex-col bg-gray-100">
        <div className="flex-1 flex justify-center items-start py-6">
          <div className="w-full max-w-6xl">
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!department) {
    return (
      <div className="flex-1 flex flex-col bg-gray-100">
        <div className="flex-1 flex justify-center items-start py-6">
          <div className="w-full max-w-6xl">
            <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
              Department not found
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="animate-fade-in">
      {/* Fixed Header */}
      <div className="border-b bg-white px-4 sm:px-6 py-4">
        <div className="max-w-6xl mx-auto w-full">
          <h2 className="text-xl sm:text-2xl font-bold text-gray-800 text-center mb-4">
            {department.name}
            {department.reference_number && (
              <span className="ml-2 text-gray-500">Ref. #{department.reference_number}</span>
            )}
          </h2>
          <div className="flex items-center gap-2">
            <div className="flex-1">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={cn(
                    "rounded-full h-2 transition-all duration-300",
                    department.progress === 100 ? "bg-green-500" :
                    department.progress >= 75 ? "bg-yellow-500" :
                    department.progress >= 40 ? "bg-orange-500" : "bg-red-500"
                  )}
                  style={{ width: `${department.progress || 0}%` }}
                />
              </div>
            </div>
            <span className="text-xs sm:text-sm font-medium text-gray-600">
              {department.progress || 0}% Complete
            </span>
            {user?.company === 'admin' && (
              <Button
                variant="outline"
                onClick={() => navigate('/checklist-management')}
                className="ml-4"
              >
                <Settings className="h-4 w-4 mr-2" />
                Manage Checklists
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex justify-center items-start p-4">
        <div className="w-full max-w-[90rem] flex flex-col gap-4 sm:gap-6">
          {/* Checklists Carousel */}
          <div className="relative bg-white/50 backdrop-blur-sm rounded-xl p-6 shadow-sm border border-gray-100">
            <div className="overflow-hidden rounded-lg" ref={emblaRef}>
              <div className="flex">
                {department.checklists
                  .sort((a, b) => formatChecklistName(a.type).localeCompare(formatChecklistName(b.type)))
                  .map((checklist) => (
                  <ChecklistPanel
                    key={checklist.type}
                    type={checklist.type as 'technicians' | 'uat' | 'cvcs' | 'advanced-staging' | 'rack-preparation' | 'engineering'}
                    checklist={checklist}
                    onToggle={handleChecklistItemToggle}
                    calculateProgress={calculateChecklistProgress}
                    updatingItems={updatingItems}
                    onFlagTask={handleFlagTask}
                    highlightedTaskId={highlightedTaskId}
                    taskRefs={taskRefs}
                    issues={department?.issues || []}
                    setEditingNoteItem={setEditingNoteItem}
                  />
                ))}
              </div>
            </div>

            {/* Carousel Navigation and Indicators */}
            <div className="flex items-center justify-center gap-6 mt-6">
              <button
                className="bg-blue-600 hover:bg-blue-700 text-white rounded-lg px-6 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                onClick={scrollPrev}
              >
                <ChevronLeft className="h-5 w-5" />
              </button>

              <div className="flex gap-3">
                {['CVCS', 'Advanced Staging', 'Technicians', 'UAT', 'Rack Preparation', 'Engineering'].map((name, index) => (
                  <button
                    key={name}
                    className={cn(
                      "w-2 h-2 rounded-full transition-all",
                      selectedChecklistIndex === index
                        ? "bg-blue-600 w-6"
                        : "bg-gray-300 hover:bg-gray-400"
                    )}
                    onClick={() => emblaApi?.scrollTo(index)}
                  />
                ))}
              </div>

              <button
                className="bg-blue-600 hover:bg-blue-700 text-white rounded-lg px-6 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                onClick={scrollNext}
              >
                <ChevronRight className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Issue Log */}
          <div className="bg-white rounded-lg shadow-sm flex flex-col h-[calc(100vh-20rem-4rem)] sm:h-[calc(100vh-20rem-6rem)]">
            <div className="border-b px-4 sm:px-6 py-4">
              <div className="flex items-center justify-between">
                <h3 className="text-base sm:text-lg font-semibold text-gray-800">Issue Log</h3>
                <Button
                  onClick={() => {
                    setEditIssue({
                      id: '',
                      title: '',
                      content: '',
                      status: 'Open',
                      created_by_username: '',
                      created_at: ''
                    });
                    setIsAddingIssue(true);
                  }}
                  className="text-white"
                >
                  New Issue
                </Button>
              </div>
              <div className="mt-4 flex flex-wrap gap-2">
                <select
                  className="text-sm border rounded-md px-2 py-1"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value as IssueStatus | 'All')}
                >
                  <option value="All">All Status</option>
                  <option value="Open">Open</option>
                  <option value="In Progress">In Progress</option>
                  <option value="Resolved">Resolved</option>
                </select>
              </div>
            </div>
            <div className="flex-1 overflow-y-auto">
              <div className="p-4 sm:p-6">
                <div className="space-y-4">
                  {department.issues?.map(issue => (
                    <div
                      key={issue.id}
                      className="group bg-gray-50 rounded-lg p-4 border border-gray-200 hover:bg-gray-100 transition-colors cursor-pointer"
                      onClick={(e) => {
                        // Prevent navigation when clicking edit/delete buttons
                        if ((e.target as HTMLElement).closest('button')) return;

                        if (issue.task_id && issue.checklist_type) {
                          handleIssueClick(issue.task_id, issue.checklist_type);
                        }
                      }}
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <IssueStatusBadge status={issue.status} />
                        </div>
                        <div className="flex items-center gap-2">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteIssue(issue.id);
                            }}
                            className="p-1.5 text-gray-400 hover:text-red-600 hover:bg-gray-100 rounded-full"
                            title="Delete issue"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              setEditingIssueId(issue.id);
                              setEditIssue(issue);
                            }}
                            className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-gray-100 rounded-full"
                            title="Edit issue"
                          >
                            <Pencil className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                      {/* Task Context Badge */}
                      {issue.checklist_type && (
                        <div className="mb-3">
                          <div className="inline-flex flex-col space-y-1">
                            <span className="inline-flex items-center">
                              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded font-medium text-sm">
                                {issue.checklist_type}
                              </span>
                              {issue.category_name && (
                                <>
                                  <span className="mx-2 text-gray-400">/</span>
                                  <span className="bg-blue-50 text-blue-600 px-2 py-1 rounded text-sm">
                                    {issue.category_name}
                                  </span>
                                </>
                              )}
                            </span>
                            {issue.task_description && (
                              <span className="text-sm text-gray-600">
                                Task: {issue.task_description}
                              </span>
                            )}
                          </div>
                        </div>
                      )}
                      <h4 className="text-sm font-medium text-gray-900 mb-2">{issue.title}</h4>
                      <p className="text-sm text-gray-700 whitespace-pre-wrap break-words mb-3">
                        {issue.content}
                      </p>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>{issue.created_by_username}</span>
                        <span>{format(new Date(issue.created_at), 'MMM d, yyyy h:mm a')}</span>
                      </div>
                    </div>
                  ))}
                  {department.issues?.length === 0 && (
                    <p className="text-gray-500 text-center py-4">No issues found. Create one using the New Issue button!</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Add/Edit Issue Modal */}
          {(isAddingIssue || editingIssueId) && (
            <div className="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center p-4">
              <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl">
                <div className="border-b px-6 py-4 flex justify-between items-center">
                  <h3 className="text-lg font-semibold text-gray-800">
                    {editingIssueId ? 'Edit Issue' : 'New Issue'}
                  </h3>
                  <button
                    onClick={() => {
                      setIsAddingIssue(false);
                      setEditingIssueId(null);
                      setEditIssue(null);
                      setFlaggedTaskContext(null);
                    }}
                    className="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full"
                  >
                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                <div className="p-6 space-y-4">
                  {!flaggedTaskContext && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Title</label>
                      <input
                        type="text"
                        className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
                        value={editIssue?.title || ''}
                        onChange={(e) => setEditIssue(prev => prev ? { ...prev, title: e.target.value } : null)}
                        placeholder="Issue title"
                      />
                    </div>
                  )}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select
                      className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
                      value={editIssue?.status || 'Open'}
                      onChange={(e) => setEditIssue(prev => prev ? { ...prev, status: e.target.value as IssueStatus } : null)}
                    >
                      <option value="Open">Open</option>
                      <option value="In Progress">In Progress</option>
                      <option value="Resolved">Resolved</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <Textarea
                      value={editIssue?.content || ''}
                      onChange={(e) => setEditIssue(prev => prev ? { ...prev, content: e.target.value } : null)}
                      className="min-h-[120px]"
                      placeholder="Describe the issue..."
                    />
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsAddingIssue(false);
                        setEditingIssueId(null);
                        setEditIssue(null);
                        setFlaggedTaskContext(null);
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      className="text-white"
                      onClick={() => {
                        if (editingIssueId) {
                          handleEditIssue();
                        } else {
                          handleAddIssue();
                        }
                      }}
                    >
                      {editingIssueId ? 'Save Changes' : 'Create Issue'}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Notes Modal */}
          {editingNoteItem && (
            <div className="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center p-4">
              <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl">
                <div className="border-b px-6 py-4 flex justify-between items-center">
                  <h3 className="text-lg font-semibold text-gray-800">Task Notes</h3>
                  <button
                    onClick={() => setEditingNoteItem(null)}
                    className="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full"
                  >
                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                <div className="p-6 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Task</label>
                    <p className="text-sm text-gray-600">{editingNoteItem.description}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Add Note</label>
                    <div className="flex gap-2">
                      <Textarea
                        value={newNote}
                        onChange={(e) => setNewNote(e.target.value)}
                        className="flex-1"
                        placeholder="Add a note..."
                      />
                      <Button
                        onClick={() => {
                          if (newNote.trim()) {
                            handleAddNote(editingNoteItem.id, newNote);
                            setNewNote('');
                          }
                        }}
                        className="self-start text-white"
                      >
                        Add
                      </Button>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <label className="block text-sm font-medium text-gray-700">Notes</label>
                    {editingNoteItem.notes?.length === 0 && (
                      <p className="text-sm text-gray-500">No notes yet. Add one above!</p>
                    )}
                    {editingNoteItem.notes?.map((note) => (
                      <div key={note.id} className="bg-gray-50 rounded-lg p-3 border border-gray-200">
                        <div className="flex justify-between items-start gap-2">
                          <p className="text-sm text-gray-700 whitespace-pre-wrap break-words">
                            {note.content}
                          </p>
                          <button
                            onClick={() => handleDeleteNote(note.id)}
                            className="p-1 text-gray-400 hover:text-red-600 hover:bg-gray-100 rounded-full"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                        <div className="mt-2 flex items-center justify-between text-xs text-gray-500">
                          <span>{note.created_by_username}</span>
                          <span>{format(new Date(note.created_at), 'MMM d, yyyy h:mm a')}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Flag Task Modal */}
      {flaggedTaskContext && (
        <div className="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl">
            <div className="border-b px-6 py-4 flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-800">
                Flag Task Issue
              </h3>
              <button
                onClick={() => {
                  setFlaggedTaskContext(null);
                  setEditIssue(null);
                }}
                className="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full"
              >
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Task</label>
                <p className="text-sm text-gray-600">{flaggedTaskContext.task.description}</p>
                <p className="text-sm text-gray-500 mt-1">
                  {flaggedTaskContext.type === 'uat' ? 'UAT Checklist' :
                   flaggedTaskContext.type === 'cvcs' ? 'CVCS Checklist' :
                   flaggedTaskContext.type === 'advanced-staging' ? 'Advanced Staging Checklist' :
                   flaggedTaskContext.type === 'rack-preparation' ? 'Rack Preparation Checklist' :
                   flaggedTaskContext.type === 'engineering' ? 'Engineering Checklist' :
                   flaggedTaskContext.type.charAt(0).toUpperCase() + flaggedTaskContext.type.slice(1) + ' Checklist'}
                  {flaggedTaskContext.categoryName && ` > ${flaggedTaskContext.categoryName}`}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Issue Description</label>
                <Textarea
                  value={editIssue?.content || ''}
                  onChange={(e) => setEditIssue(prev => prev ? { ...prev, content: e.target.value } : null)}
                  className="min-h-[120px]"
                  placeholder="Describe the issue with this task..."
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setFlaggedTaskContext(null);
                    setEditIssue(null);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  className="text-white"
                  onClick={handleAddIssue}
                >
                  Create Issue
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}