-- Rename checklist types: cvcs -> rack-preparation, advanced-staging -> engineering

-- Temporarily allow old and new values in constraints
ALTER TABLE public.checklists DROP CONSTRAINT IF EXISTS checklists_type_check;

ALTER TABLE public.checklists
ADD CONSTRAINT checklists_type_check
CHECK (type IN ('technicians', 'uat', 'cvcs', 'advanced-staging', 'rack-preparation', 'engineering'));

-- Update checklist type values
UPDATE public.checklists
SET type = 'rack-preparation'
WHERE type = 'cvcs';

UPDATE public.checklists
SET type = 'engineering'
WHERE type = 'advanced-staging';

-- Update template type values
ALTER TABLE public.checklist_templates DROP CONSTRAINT IF EXISTS checklist_templates_type_check;

ALTER TABLE public.checklist_templates
ADD CONSTRAINT checklist_templates_type_check
CHECK (type IN ('technicians', 'uat', 'cvcs', 'advanced-staging', 'rack-preparation', 'engineering'));

UPDATE public.checklist_templates
SET type = 'rack-preparation'
WHERE type = 'cvcs';

UPDATE public.checklist_templates
SET type = 'engineering'
WHERE type = 'advanced-staging';

-- Update issue checklist types
UPDATE public.department_issues
SET checklist_type = 'rack-preparation'
WHERE checklist_type = 'cvcs';

UPDATE public.department_issues
SET checklist_type = 'engineering'
WHERE checklist_type = 'advanced-staging';

-- Rename department boolean flags
ALTER TABLE public.departments 
RENAME COLUMN has_cvcs_checklist TO has_rack_preparation_checklist;

ALTER TABLE public.departments 
RENAME COLUMN has_advanced_staging_checklist TO has_engineering_checklist;

-- Re-apply final constraints with only new values
ALTER TABLE public.checklists DROP CONSTRAINT IF EXISTS checklists_type_check;

ALTER TABLE public.checklists
ADD CONSTRAINT checklists_type_check
CHECK (type IN ('technicians', 'uat', 'rack-preparation', 'engineering'));

ALTER TABLE public.checklist_templates DROP CONSTRAINT IF EXISTS checklist_templates_type_check;

ALTER TABLE public.checklist_templates
ADD CONSTRAINT checklist_templates_type_check
CHECK (type IN ('technicians', 'uat', 'rack-preparation', 'engineering')); 