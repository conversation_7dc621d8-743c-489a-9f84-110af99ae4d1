// Main dashboard view showing all BEINCOURT projects
import React, { useEffect, useState } from 'react';
import { ProjectList } from './ProjectList';
import { getProjects } from '../lib/db';
import type { Database } from '../types/supabase';
import { useUser } from '@/contexts/UserContext';

type Project = Database['public']['Tables']['projects']['Row'];

export function Dashboard() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useUser();

  useEffect(() => {
    async function loadData() {
      try {
        const projectsData = await getProjects();

        // Filter out CMP project for JUDCO users
        if (user?.company === 'JUDCO') {
          const filteredProjects = projectsData.filter(project =>
            !project.name.includes('CMP')
          );
          setProjects(filteredProjects);
        } else {
          setProjects(projectsData);
        }
      } catch (err) {
        console.error('Error loading projects:', err);
        setError('Failed to load projects. Please try again later.');
      } finally {
        setLoading(false);
      }
    }

    loadData();
  }, [user]);

  if (loading) {
    return (
      <div className="flex-1 p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mx-auto mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto mb-8"></div>
          <div className="h-[calc(100vh-22rem)] bg-gray-100 rounded-xl"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 p-6">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 p-6">
      {/* Dashboard header */}
      <div className="mb-8 text-center">
        <h2 className="text-2xl font-bold text-gray-800">Projects Overview</h2>
        <p className="text-gray-600">
          {user?.company === 'JUDCO'
            ? 'Track and manage JUDCO projects'
            : 'Track and manage all projects'}
        </p>
      </div>

      {/* Projects list view */}
      {projects.length > 0 ? (
        <ProjectList projects={projects} />
      ) : (
        <div className="text-center text-gray-500">
          No projects found.
        </div>
      )}
    </div>
  );
}