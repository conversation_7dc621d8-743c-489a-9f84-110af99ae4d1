# Product Requirements Document: BIC Dashboard

## 1. Introduction

* **Purpose**: This document lays out what's needed for the BIC Dashboard project. By building a React/TypeScript web app with Vite to help manage projects within BIC, track progress across different departments, handle checklists, log issues, and provide real-time updates.
* **Vision**: This application aims to give BIC engineers, technicians, and project managers a straightforward, easy-to-use web interface. It should help them keep an eye on project progress, department status, and task completion while providing tools for issue tracking and collaboration.
* **Scope**: This version focuses on the core project management and tracking features. By using React's component architecture, Supabase for backend and real-time updates, the Context API for state management, custom hooks for business logic, and React Router for navigation.

## 2. Goals

*Here's the steps to achieve the BIC Dashboard.*

* **Goal 1**: Create a central dashboard where BIC employees can track and manage projects, departments, and tasks. The aim is to cut down the time spent on manual tracking and status updates by about 40% within six months after launch.
* **Goal 2**: Make project and department status easier to see and understand for everyone involved, using clear visuals and good filtering. This should help teams make quicker, smarter decisions about project management.
* **Goal 3**: Build a solid React/TypeScript base with Supabase integration that can easily be added to later (like new data sources or more advanced analytics) without slowing things down. Core interactions should take less than 2 seconds.

## 3. Target Audience

*Who's this dashboard for?*

* **Primary Users**: BIC engineers, technicians, and project managers. These are the people who need to regularly check project status, complete checklists, log issues, and monitor department progress for their day-to-day work.
* **Secondary Users**: Executive leadership and other project managers from partner companies (BEINCOURT, CVCS, JUDCO). They're looking for high-level summaries, project progress indicators, and issue logs for big-picture decisions and oversight.

## 4. Functional Requirements (Using MoSCoW Method)

*Here are the specific features the dashboard needs, broken down by priority (Must Have, Should Have, Could Have, Won't Have).*

### Must Have (M) - Core Stuff Needed for Launch
*These features are absolutely essential for this version.*

* **Feature 1: Authentication**
    * **Description:** Secure login/logout and company-based access control.
    * **Details:** Users log in with their BIC credentials via Supabase authentication. Different companies (BEINCOURT, CVCS, JUDCO, admin) see different projects and have different access levels.
    * **Implementation Tasks:**
        - [x] Implement Supabase authentication for user management.
        - [x] Create `UserContext` and `useUser` hook for auth state (user info, company, session).
        - [x] Build login page with username/password authentication.
        - [x] Implement registration with access key protection.
        - [x] Implement logout functionality.
        - [x] Develop `ProtectedRoute` to restrict access based on auth status.
        - [x] Store user profile info (username, company) in `UserContext`.
        - [x] Implement company-based access control for projects.

* **Feature 2: Project Management**
    * **Description:** The main dashboard showing all accessible projects with status and progress.
    * **Details:** The homepage displays projects with progress indicators, filtering by company access rules (JUDCO users only see San Diego project, etc.).
    * **Implementation Tasks:**
        - [x] Design dashboard homepage layout with project cards (React components, Tailwind).
        - [x] Create project data structure and database schema in Supabase.
        - [x] Implement project filtering based on user's company.
        - [x] Create a `ProjectList` component with search functionality.
        - [x] Develop data fetching hooks for projects with loading/error states.
        - [x] Add visual progress indicators for project completion percentage.
        - [x] Implement loading state indicators for initial data load.
        - [x] Add error handling for failed data fetches.
        - [x] Ensure responsive layout for desktop/laptop screens.

* **Feature 3: Department Management**
    * **Description:** View and manage departments within courthouses/projects.
    * **Details:** Users can see all departments in a courthouse, filter by status, and access detailed department views.
    * **Implementation Tasks:**
        - [x] Create department data structure and database schema.
        - [x] Implement department listing page with filtering options.
        - [x] Add status indicators (on-track, delayed, complete) for departments.
        - [x] Create progress visualization for department completion.
        - [x] Implement search functionality for departments.
        - [x] Add filtering by status (all, incomplete, in-progress, complete, open-issues).
        - [x] Create department detail page with all associated checklists.
        - [x] Implement birds-eye view for department overview.

* **Feature 4: Checklist Management**
    * **Description:** Track and update task checklists for departments.
    * **Details:** Different checklist types (technicians, UAT, CVCS, advanced-staging, rack-preparation, engineering) with items that can be checked off and annotated.
    * **Implementation Tasks:**
        - [x] Design checklist data structure with categories and items.
        - [x] Create checklist components for displaying and interacting with tasks.
        - [x] Implement item completion tracking with user attribution.
        - [x] Add timestamp recording for completed items.
        - [x] Create progress calculation for checklist completion.
        - [x] Implement access control for checklist item completion.
        - [x] Add note functionality for checklist items.
        - [x] Create checklist management page for administration.

* **Feature 5: Issue Tracking**
    * **Description:** Log and track issues for departments and checklist items.
    * **Details:** Users can flag issues, assign status (Open, In Progress, Resolved), and track them across the system.
    * **Implementation Tasks:**
        - [x] Create issue data structure and database schema.
        - [x] Implement issue creation from department and checklist views.
        - [x] Add issue status management (Open, In Progress, Resolved).
        - [x] Create issue log page with filtering by status.
        - [x] Implement issue detail view with status updates.
        - [x] Add visual indicators for departments with open issues.
        - [x] Create issue count badges for quick reference.

* **Feature 6: Navigation**
    * **Description:** Easy-to-use multi-page navigation using client-side routing.
    * **Details:** Users can move between projects, courthouses, departments, and system pages using a persistent nav bar.
    * **Implementation Tasks:**
        - [x] Set up `react-router-dom` for client-side routing.
        - [x] Design and implement main `Layout` component with header navigation.
        - [x] Define routes for all primary sections.
        - [x] Create back button functionality for nested navigation.
        - [x] Implement user profile dropdown in navigation.
        - [x] Add mobile-responsive menu toggle.
        - [x] Clearly indicate active navigation state.

* **Feature 7: Real-time Updates**
    * **Description:** Live data updates without page refreshes.
    * **Details:** Changes to checklists, issues, and department status update in real-time across all connected clients.
    * **Implementation Tasks:**
        - [x] Implement Supabase real-time subscriptions.
        - [x] Create real-time hooks for checklist updates.
        - [x] Add real-time issue status tracking.
        - [x] Implement optimistic UI updates for better user experience.
        - [x] Add visual indicators for real-time changes.
        - [x] Ensure proper error handling for subscription failures.

### Should Have (S) - Important, but Not Essential for Launch
*These features would add a lot of value, but it can launch without them if needed.*

* **Feature 1: Advanced Filtering and Search**
    * **Description:** Enhanced filtering and search capabilities across the application.
    * **Details:** More advanced filtering options for projects, departments, and issues, with saved filter preferences.
    * **Implementation Tasks:**
        - [x] Implement basic search for projects and departments.
        - [x] Add status filtering for departments (all, incomplete, in-progress, complete, open-issues).
        - [x] Create issue filtering by status (Open, In Progress, Resolved, All).
        - [ ] Add date range filtering for issues and completed tasks.
        - [ ] Implement saved filter preferences in localStorage.
        - [ ] Create advanced search with multiple criteria.
        - [ ] Add filter history for quick access to previous searches.

* **Feature 2: Data Visualization**
    * **Description:** Visual representations of project and department progress.
    * **Details:** Charts and graphs showing completion rates, issue trends, and department status.
    * **Implementation Tasks:**
        - [x] Implement progress bars for project and department completion.
        - [x] Create visual status indicators (color coding for on-track, delayed, complete).
        - [x] Add birds-eye view visualization for department overview.
        - [ ] Create trend charts for issue resolution over time.
        - [ ] Implement completion rate charts for departments and projects.
        - [ ] Add tooltips for detailed information on hover.
        - [ ] Ensure visualizations are consistent with the application theme.

* **Feature 3: Data Export**
    * **Description:** Export project, department, and issue data for offline analysis.
    * **Details:** Allow users to export data to CSV format for further processing or reporting.
    * **Implementation Tasks:**
        - [ ] Create utility functions for CSV export.
        - [ ] Add export buttons to project list, department list, and issue log.
        - [ ] Implement data formatting for export.
        - [ ] Add export options (all data vs. filtered data).
        - [ ] Handle large data exports efficiently.
        - [ ] Provide feedback during export process.

* **Feature 4: Theme Support**
    * **Description:** Light and dark mode theme options.
    * **Details:** Allow users to switch between light and dark color schemes based on preference.
    * **Implementation Tasks:**
        - [ ] Configure Tailwind for dark mode support.
        - [ ] Create theme toggle component in user profile dropdown.
        - [ ] Implement theme state management with Context API.
        - [ ] Store theme preference in localStorage.
        - [ ] Ensure all components respect the selected theme.
        - [ ] Add system preference detection for automatic theme selection.

* **Feature 5: User Profile Management**
    * **Description:** Enhanced user profile features and settings.
    * **Details:** Allow users to update profile information, change passwords, and manage preferences.
    * **Implementation Tasks:**
        - [x] Implement basic user profile display in dropdown.
        - [x] Add password change functionality.
        - [ ] Create user settings page.
        - [ ] Add profile picture upload capability.
        - [ ] Implement notification preferences.
        - [ ] Add user activity history.
        - [ ] Create user preference management interface.

### Could Have (C) - Nice to Have, Less Critical
*These would be good additions if they don't delay higher priority items.*

* **Feature 1: Advanced Notifications**
    * **Description:** In-app notification system for important events and updates.
    * **Details:** Notify users about issue status changes, checklist completions, or new assignments.
    * **Implementation Tasks:**
        - [ ] Design notification components (toast, banner, badge).
        - [ ] Implement notification storage in database.
        - [ ] Create real-time notification delivery using Supabase.
        - [ ] Add notification center in user interface.
        - [ ] Implement notification preferences and settings.
        - [ ] Add read/unread status tracking.

* **Feature 2: Customizable Dashboard**
    * **Description:** Allow users to personalize their dashboard view.
    * **Details:** Drag-and-drop interface for reordering projects, custom filters, and saved views.
    * **Implementation Tasks:**
        - [ ] Implement drag-and-drop with DND Kit for project cards.
        - [ ] Create saved view functionality for dashboard layouts.
        - [ ] Add customizable widgets for different data views.
        - [ ] Store user's layout preferences in database.
        - [ ] Create dashboard settings panel.
        - [ ] Add reset to default option.

* **Feature 3: Advanced Reporting**
    * **Description:** Generate detailed reports on project and department status.
    * **Details:** Create customizable reports with various metrics and visualizations.
    * **Implementation Tasks:**
        - [ ] Design report templates for different data types.
        - [ ] Create report builder interface.
        - [ ] Implement PDF export functionality.
        - [ ] Add scheduled report generation.
        - [ ] Create report sharing capabilities.
        - [ ] Implement report history and archiving.

* **Feature 4: Task Assignment and Tracking**
    * **Description:** Assign checklist items and issues to specific users.
    * **Details:** Track who is responsible for completing tasks and resolving issues.
    * **Implementation Tasks:**
        - [x] Track who completed checklist items and when.
        - [ ] Add explicit task assignment functionality.
        - [ ] Implement task due dates and reminders.
        - [ ] Create user workload view.
        - [ ] Add task priority levels.
        - [ ] Implement task dependencies.

* **Feature 5: Collaboration Tools**
    * **Description:** Enhanced collaboration features for team communication.
    * **Details:** Comments, mentions, and discussion threads for issues and tasks.
    * **Implementation Tasks:**
        - [x] Basic notes functionality for checklist items.
        - [ ] Implement threaded comments on issues.
        - [ ] Add @mentions for user notifications.
        - [ ] Create activity feeds for departments and projects.
        - [ ] Implement file attachments for issues and comments.
        - [ ] Add real-time comment updates.

* **Feature 6: Mobile Optimization**
    * **Description:** Enhanced mobile experience beyond basic responsiveness.
    * **Details:** Optimized mobile layouts and touch interactions for field use.
    * **Implementation Tasks:**
        - [x] Basic responsive design for core functionality.
        - [ ] Create mobile-optimized checklist interfaces.
        - [ ] Implement touch-friendly controls for field use.
        - [ ] Add offline capability for checklist completion.
        - [ ] Optimize performance for mobile networks.
        - [ ] Create mobile-specific navigation patterns.

### Won't Have (W) - Not in this version
*To keep focus and deliver core value on time, these features are out of scope for this version:*

* **Native Mobile App**: No dedicated iOS/Android app. Focus on responsive web design instead.
* **Analytics**: No insights or predictive analytics.
* **Multi-Language Support**: UI will be English-only for this version.
* **Server-Side Rendering (SSR)**: This version will remain a Client-Side Rendered (CSR) app.
* **Third-party Integrations**: Limited to Supabase for backend. No other third-party service integrations.
* **Automated Reporting/Emailing**: No automated generation or email delivery of reports.
* **Advanced User Management**: User creation and role assignment handled through admin interface only.
* **Public API**: No public API for third-party integrations in this version.
* **Advanced Analytics**: No complex data analysis or business intelligence features.
* **Gantt Charts/Timeline Views**: No timeline-based project visualization in initial version.

## 5. Non-Functional Requirements

*These are about the quality and operation of the system.*

* **Performance**:
    * Initial dashboard load (LCP) under 2.5 seconds on a standard corporate network.
    * Real-time updates should be delivered within 1 second of changes.
    * Checklist interactions (checking items, adding notes) should feel immediate.
    * Progress calculations and status updates should be efficient.
    * Optimize Supabase queries and real-time subscriptions.
    * Implement optimistic UI updates for better perceived performance.
    * Optimize app bundle size (code splitting, tree shaking).
* **Scalability**:
    * Database schema designed to handle hundreds of projects and thousands of departments.
    * Frontend architecture (React components, state management) must support adding new features without major refactoring.
    * Real-time subscription management to prevent connection limits.
    * Efficient data fetching with pagination for large data sets.
    * State management (Context API) organized to prevent performance bottlenecks.
* **Usability**:
    * Intuitive interface requiring minimal training for BIC staff and contractors.
    * Consistent UI patterns for navigation, filtering, and checklist interactions.
    * Clear visual indicators for status, progress, and issues.
    * Responsive design optimized for desktop/laptop with basic mobile support.
    * Efficient workflows for common tasks (completing checklist items, logging issues).
    * WCAG complient.
* **Security**:
    * All communication via HTTPS.
    * Secure authentication through Supabase.
    * Row-level security policies in database.
    * Company-based access control for projects and data.
    * Secure password storage and management.
    * Protection against common web vulnerabilities (XSS, CSRF).
    * Input validation for all user inputs.
* **Maintainability**:
    * TypeScript throughout for type safety.
    * Consistent code style (ESLint, Prettier).
    * Modular architecture with clear separation of concerns.
    * Component-based design with reusable UI elements.
    * Well-organized project structure.
    * Clear comments for complex logic.
    * Version control with Git.
* **Reliability**:
    * Graceful error handling for API failures with informative messages.
    * Offline data persistence where possible.
    * Data validation to prevent corruption.
    * Automatic reconnection for real-time subscriptions.
    * Cross-browser compatibility (latest versions of Chrome, Firefox, Safari, Edge).
    * Regular database backups.

## 6. Technical Architecture (High-Level)

*A quick look at the main tech and architectural choices.*

* **Frontend Framework**: React (v18+) with TypeScript.
* **Build Tool**: Vite.
* **Backend & Database**: Supabase (PostgreSQL database, authentication, real-time subscriptions).
* **State Management**: React Context API with custom hooks for global state (User, Filters). Component state with `useState`/`useReducer`.
* **Routing**: `react-router-dom` (v6+).
* **Data Fetching**: Custom hooks using Supabase client for database operations and real-time subscriptions.
* **UI Components**: Custom React components and Shadcn UI, styled with Tailwind CSS.
* **Styling**: Tailwind CSS for utility-first styling with custom theme configuration.
* **Animation**: Framer Motion for transitions and interactive elements.
* **Forms**: React Hook Form with Zod for validation.
* **Drag & Drop**: DND Kit for sortable interfaces.
* **Date Handling**: date-fns for date manipulation and formatting.
* **Linting/Formatting**: ESLint and Prettier.
* **Testing**: Jest and React Testing Library for unit/integration tests.

## 7. Design Considerations

*Guiding principles for UI/UX.*

* **UI/UX**: Clean, professional interface with a focus on usability for project management. Navy blue primary color scheme reflecting BIC's branding.
* **Component Hierarchy**: Organized around pages (Dashboard, ProjectPage, DepartmentDetailsPage), layout components (Layout, Navigation), feature components (ProjectList, DepartmentList, ChecklistPanel), and reusable UI components from Shadcn UI.
* **Design System**: Consistent color scheme, typography, and spacing via Tailwind CSS with custom theme configuration. Dark mode support in the CSS variables but not yet implemented in the UI.
* **Accessibility**: Basic accessibility considerations including color contrast, keyboard navigation, and semantic HTML.
* **Responsive Design**: Primary focus on desktop/laptop with basic mobile support through responsive breakpoints.

## 8. Open Issues / Questions

*Things that still need to be figured out.*

* How to handle very large projects with hundreds of departments? Consider pagination or virtualized lists.
* Best approach for implementing advanced filtering across multiple data types?
* Strategy for offline support in areas with poor connectivity?
* Performance optimization for real-time subscriptions with many concurrent users?
* Backup and disaster recovery procedures for Supabase database?
* User onboarding process and documentation needs?
* Approach for implementing theme support with existing Shadcn UI components?
* Requirements for data export formats and frequency?
* Strategy for implementing advanced reporting features?
* Deployment and CI/CD pipeline configuration?

## 9. Future Considerations

*Ideas for later versions, beyond this version.*

* Advanced analytics and reporting with visualization dashboards.
* Enhanced mobile experience with offline support for field use.
* Integration with third-party project management tools.
* Automated notifications via email or push notifications.
* Enhanced collaboration features with comments and @mentions.
* Timeline views and Gantt charts for project scheduling.
* Document management and file attachments for departments and issues.
* Advanced user management with fine-grained permissions.
* Multi-language support for international teams.
* Integration with IoT sensors for automated status updates.
* Public API for third-party integrations.
* Enhanced security features like two-factor authentication.
* Audit logging for compliance and accountability.

---
*Document Version: 7.0*
*Last Updated: May 1, 2025*
