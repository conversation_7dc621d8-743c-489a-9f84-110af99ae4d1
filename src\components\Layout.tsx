// Main application layout with header and navigation
import React, { useState } from 'react';
import { Home, Menu, X } from 'lucide-react';
import { useUser } from '../contexts/UserContext';
import { BackButton } from './BackButton';
import { Outlet, Link, useLocation } from 'react-router-dom';
import { ProfileDropdown } from './ProfileDropdown';

export function Layout() {
  const { user } = useUser();
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const navLinkClass = (active: boolean) =>
    `flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
      active ? 'bg-blue-900 text-white' : 'hover:bg-blue-900 text-white'
    }`;

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header with navigation and user controls */}
      <header className="fixed top-0 z-40 w-full bg-[#000033] text-white shadow-lg">
        <div className="w-full">
          <div className="flex h-16 items-center justify-between px-4 md:px-6">
            {/* Logo and branding */}
            <div className="flex items-center space-x-2 md:space-x-4">
              <BackButton className="text-white hover:bg-blue-900" />
              <div className="flex items-center space-x-2 md:space-x-3">
                <img 
                  src="https://i.imgur.com/VcanTGH.png" 
                  alt="BEINCOURT" 
                  className="h-6 w-auto md:h-8"
                />
                <h1 className="text-lg md:text-xl font-semibold tracking-wider">
                  DASHBOARD
                </h1>
              </div>
            </div>

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="md:hidden rounded-lg p-2 hover:bg-blue-900"
            >
              {isMobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>

            {/* Desktop Navigation Links and User Controls */}
            <div className="hidden md:flex items-center justify-end space-x-4 flex-1 ml-4">
              <nav className="flex items-center space-x-4">
                <Link to="/" className={navLinkClass(isActive('/'))}>
                  <Home className="h-5 w-5" />
                  <span>Home</span>
                </Link>
              </nav>

              <div className="flex items-center">
                <ProfileDropdown />
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Navigation Menu - Slide from right */}
      <div 
        className={`fixed inset-y-0 right-0 z-50 w-64 bg-[#000033] transform transition-transform duration-300 ease-in-out ${
          isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        <div className="flex flex-col h-full">
          {/* Close button */}
          <div className="flex justify-end p-4">
            <button
              onClick={() => setIsMobileMenuOpen(false)}
              className="rounded-lg p-2 hover:bg-blue-900"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Mobile menu content */}
          <nav className="flex-1 px-4">
            {/* Profile */}
            <div className="mb-4">
              <div className="w-full text-white px-2">
                <ProfileDropdown />
              </div>
            </div>

            {/* Navigation Links */}
            <Link 
              to="/" 
              className={navLinkClass(isActive('/'))}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              <Home className="h-5 w-5" />
              <span>Home</span>
            </Link>
          </nav>
        </div>
      </div>

      {/* Backdrop overlay */}
      {isMobileMenuOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Main content */}
      <main className="flex-1 overflow-y-auto pt-16">
        <Outlet />
      </main>
    </div>
  );
}