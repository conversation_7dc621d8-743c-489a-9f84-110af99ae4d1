import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from './ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from './ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from './ui/form';
import { Input } from './ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from './ui/select';
import { useToast } from './ui/use-toast';
import { createDepartment } from '../lib/db';
import { supabase } from '../lib/supabase';
import { Plus } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';

const formSchema = z.object({
  name: z.string().min(1, 'Department name is required'),
  referenceNumber: z.number().min(1, 'Reference number must be positive'),
  floor: z.number().min(1, 'Floor must be positive'),
  selectedChecklistTypes: z.array(z.string()).optional().default([]),
});

interface CreateDepartmentDialogProps {
  courthouseId: string;
  onDepartmentCreated?: () => void;
}

interface ChecklistType {
  type: string;
}

type FormValues = z.infer<typeof formSchema>;

export function CreateDepartmentDialog({ courthouseId, onDepartmentCreated }: CreateDepartmentDialogProps) {
  const [open, setOpen] = React.useState(false);
  const [availableChecklistTypes, setAvailableChecklistTypes] = useState<string[]>([]);
  const [isLoadingTypes, setIsLoadingTypes] = useState(false);
  const { toast } = useToast();
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      referenceNumber: 1,
      floor: 1,
      selectedChecklistTypes: [],
    },
  });

  useEffect(() => {
    if (open) {
      const fetchTypes = async () => {
        setIsLoadingTypes(true);
        try {
          const { data, error } = await supabase
            .from('checklist_templates')
            .select('type');

          if (error) throw error;

          const uniqueTypes = Array.from(new Set(data.map((item: ChecklistType) => item.type)));
          const formattedTypes = uniqueTypes.map(type => 
            type.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')
          );
          setAvailableChecklistTypes(formattedTypes.sort());
          
        } catch (error) {
          console.error('Error fetching checklist types:', error);
          toast({
            title: 'Error',
            description: 'Failed to load available checklist types.',
            variant: 'destructive',
          });
          setAvailableChecklistTypes([]);
        } finally {
          setIsLoadingTypes(false);
        }
      };
      void fetchTypes();
    } else {
      form.reset();
      setAvailableChecklistTypes([]);
    }
  }, [open, form, toast]);

  async function onSubmit(values: FormValues) {
    try {
      const departmentData = {
        courthouseId,
        name: values.name,
        referenceNumber: values.referenceNumber,
        floor: values.floor,
        selectedChecklistTypes: values.selectedChecklistTypes,
      };

      console.log("Submitting department data:", departmentData);

      await createDepartment(departmentData);

      setOpen(false);
      onDepartmentCreated?.();

      toast({
        title: 'Success',
        description: 'Department created successfully',
      });
    } catch (error) {
      console.error('Error creating department:', error);
      toast({
        title: 'Error',
        description: 'Failed to create department. Please try again.',
        variant: 'destructive',
      });
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Plus className="h-4 w-4" />
          New Department
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Create New Department</DialogTitle>
          <DialogDescription>
            Add a new department to this courthouse. Select the checklists it requires.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter department name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="referenceNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Reference Number</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        min="1"
                        placeholder="Enter reference number"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="floor"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Floor</FormLabel>
                    <FormControl>
                      <Input 
                        type="number"
                        min="1"
                        placeholder="Enter floor number"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormItem>
              <FormLabel>Required Checklists</FormLabel>
              <FormDescription>
                Select the checklists that apply to this department.
              </FormDescription>
              <ScrollArea className="h-40 w-full rounded-md border p-4 mt-2">
                {isLoadingTypes ? (
                  <p className="text-sm text-muted-foreground">Loading checklist types...</p>
                ) : availableChecklistTypes.length > 0 ? (
                  availableChecklistTypes.map((type) => (
                    <FormField
                      key={type}
                      control={form.control}
                      name="selectedChecklistTypes"
                      render={({ field }) => {
                        const typeId = type.toLowerCase().replace(/\s+/g, '-'); 
                        return (
                          <FormItem
                            key={typeId}
                            className="flex flex-row items-start space-x-3 space-y-0 py-2"
                          >
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes(typeId)}
                                onCheckedChange={(checked: boolean | 'indeterminate') => {
                                  return checked === true
                                    ? field.onChange([...(field.value || []), typeId])
                                    : field.onChange(
                                        (field.value || []).filter(
                                          (value) => value !== typeId
                                        )
                                      );
                                }}
                              />
                            </FormControl>
                            <FormLabel className="font-normal">
                              {type}
                            </FormLabel>
                          </FormItem>
                        );
                      }}
                    />
                  ))
                ) : (
                  <p className="text-sm text-muted-foreground">No checklist templates found.</p>
                )}
              </ScrollArea>
              <FormMessage />
            </FormItem>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setOpen(false)}>Cancel</Button>
              <Button type="submit" disabled={form.formState.isSubmitting || isLoadingTypes}>
                {form.formState.isSubmitting ? 'Creating...' : 'Create Department'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 
