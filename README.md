# BIC Dashboard

This dashboard helps manage projects within BIC. It tracks progress across different departments, handles checklists, logs issues, and provides real-time updates.

## Project Layout

```
bic-dashboard/
├── src/                    # Main source code
│   ├── assets/             # Images, icons, etc.
│   ├── components/         # Reusable React pieces (UI bits)
│   │   └── ui/            # Base components from Shadcn UI
│   ├── contexts/           # React Context for global state
│   ├── data/               # Static data (like dropdown options)
│   ├── db/                 # Database related code (currently unused, see supabase/)
│   ├── hooks/              # Custom React Hooks
│   │   └── realtime/      # Hooks for Supabase real-time features
│   ├── lib/               # Utility functions, API clients
│   ├── pages/             # Top-level page components
│   ├── scripts/           # Helper scripts
│   ├── types/             # TypeScript definitions
│   ├── utils/             # General utility functions
│   ├── App.tsx            # Main application component
│   ├── index.css          # Global CSS styles
│   └── main.tsx           # App entry point (where React starts)
│
├── supabase/               # Supabase config, migrations, and database functions
│   ├── functions/         # Postgres functions (RLS helpers, etc.)
│   └── migrations/        # Database schema changes
├── public/                 # Files served directly by the web server
├── index.html             # Main HTML page
├── package.json           # Project dependencies and scripts
├── README.md              # This file
└── tsconfig.json          # TypeScript configuration
```
*(Other config files like `vite.config.ts`, `postcss.config.js`, `tailwind.config.js` are also present)*

## Tech Stack

- **Frontend:** React (v18+) with TypeScript, Vite for building
- **Styling:** TailwindCSS & Shadcn UI
- **Backend & Database:** Supabase (handles auth, database, real-time)
- **Animation:** Framer Motion
- **Routing:** React Router (v6+)
- **Forms:** React Hook Form with Zod for validation
- **Drag & Drop:** DND Kit
- **Carousel:** Embla Carousel
- **Dates:** date-fns

## Features

- Login system for users.
- Access control based on user's company (BEINCOURT, CVCS, JUDCO, admin).
- Live data updates without needing to refresh.
- Manage projects, courthouses, and departments.
- Detailed checklists for tracking tasks within departments.
- Ability to add notes and log issues for tasks or departments.
- Visual progress tracking for checklists and overall project status.
- Responsive design that works on different screen sizes.
- Tracks who completed each checklist item and when.

## Access Control

### Company Rules

- Users belong to a company (BEINCOURT, CVCS, JUDCO, admin).
- **JUDCO:** Can only see the San Diego project.
- **BEINCOURT/CVCS:** See their assigned projects.
- **admin:** Can see everything.

### Checklist Rules

- You can't uncheck a task that someone else completed.

## Database Overview

- **Projects:** The main construction projects.
- **Courthouses:** Buildings within a project.
- **Departments:** Areas/sections within a courthouse.
- **Checklists:** Task lists for departments (e.g., Technician tasks, UAT).
- **Checklist Items:** Individual tasks on a checklist.
- **Notes:** Comments on checklist items or departments.
- **Issues:** Problems flagged for departments or specific tasks.