-- Fix checklist items that duplicate their category name but aren't headers.

-- Remove non-header items where description matches category name.
DELETE FROM checklist_items
WHERE id IN (
  SELECT ci.id
  FROM checklist_items ci
  JOIN checklist_categories cc ON ci.category_id = cc.id
  WHERE ci.description = cc.name AND ci.is_header = false
);

-- Ensure a header item (is_header=true, position=0) exists for every category that has items.
INSERT INTO checklist_items (id, checklist_id, category_id, description, is_header, position)
SELECT 
  CONCAT(c.id, '_', cc.id, '_header_', md5(random()::text)) as id,
  c.id as checklist_id,
  cc.id as category_id,
  cc.name as description,
  true as is_header,
  0 as position  -- Headers should be position 0
FROM checklists c
CROSS JOIN checklist_categories cc
LEFT JOIN checklist_items existing_header ON 
  c.id = existing_header.checklist_id AND
  cc.id = existing_header.category_id AND
  existing_header.is_header = true
LEFT JOIN checklist_items existing_item ON
  c.id = existing_item.checklist_id AND
  cc.id = existing_item.category_id
WHERE 
  existing_header.id IS NULL AND  -- No header exists
  existing_item.id IS NOT NULL    -- But items exist for this category
ON CONFLICT DO NOTHING;

-- Renumber positions: Headers are 0, others follow sequentially.
WITH numbered_items AS (
  SELECT 
    ci.id,
    ROW_NUMBER() OVER (
      PARTITION BY ci.checklist_id, ci.category_id 
      ORDER BY 
        ci.is_header DESC,   -- Headers first
        ci.position NULLS LAST,
        ci.description
    ) - 1 as new_position
  FROM checklist_items ci
)
UPDATE checklist_items
SET position = ni.new_position
FROM numbered_items ni
WHERE checklist_items.id = ni.id; 