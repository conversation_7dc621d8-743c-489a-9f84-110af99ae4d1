// Root application component with routing and global state
import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Layout } from './components/Layout';
import { Dashboard } from './components/Dashboard';
import { ProjectPage } from './pages/ProjectPage';
import { DepartmentsPage } from './pages/DepartmentsPage';
import { DepartmentDetailsPage } from './pages/DepartmentDetailsPage';
import { DepartmentBirdsEyeView } from './pages/DepartmentBirdsEyeView';
import { IssueLogPage } from './pages/IssueLogPage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import { UserProvider, useUser } from './contexts/UserContext';
import { ChecklistManagementPage } from './pages/ChecklistManagementPage';

const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { user } = useUser();
  if (!user) {
    return <Navigate to="/login" replace />;
  }
  return <>{children}</>;
};

export default function App() {
  return (
    <UserProvider>
      <Router>
        <Routes>
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<RegisterPage />} />
          <Route
            element={
              <ProtectedRoute>
                <Layout />
              </ProtectedRoute>
            }
          >
            <Route index element={<Dashboard />} />
            <Route path="/project/:projectId" element={<ProjectPage />} />
            <Route path="/courthouse/:courthouseId" element={<DepartmentsPage />} />
            <Route path="/department/:departmentId" element={<DepartmentDetailsPage />} />
            <Route path="/departments/birds-eye" element={<DepartmentBirdsEyeView />} />
            <Route path="/departments/issues" element={<IssueLogPage />} />
            <Route path="/checklist-management" element={<ChecklistManagementPage />} />
          </Route>
          <Route path="*" element={<Navigate to="/login" replace />} />
        </Routes>
      </Router>
    </UserProvider>
  );
}