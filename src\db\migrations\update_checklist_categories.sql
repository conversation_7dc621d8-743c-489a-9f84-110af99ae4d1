-- Ensures rack-prep and engineering checklists have correct categories and items.

-- Add missing category headers for checklists where items exist but no header.
WITH checklist_template_categories AS (
  SELECT DISTINCT type, category_id 
  FROM checklist_templates 
  WHERE type IN ('rack-preparation', 'engineering')
)
INSERT INTO checklist_items (
  id,
  checklist_id, 
  category_id, 
  description,
  completed,
  is_header,
  position,
  created_at,
  updated_at
)
SELECT 
  gen_random_uuid(),
  c.id,
  ctc.category_id,
  'Category Header',
  false,
  true,
  0,
  now(),
  now()
FROM checklists c
CROSS JOIN checklist_template_categories ctc
WHERE c.type = ctc.type
AND NOT EXISTS (
  SELECT 1 
  FROM checklist_items ci 
  WHERE ci.checklist_id = c.id 
  AND ci.category_id = ctc.category_id
)
GROUP BY c.id, ctc.category_id;

-- Add missing checklist items based on templates.
INSERT INTO checklist_items (
  id, 
  checklist_id, 
  category_id, 
  description, 
  completed, 
  is_header, 
  position, 
  template_id,
  created_at, 
  updated_at
)
SELECT 
  gen_random_uuid(),
  c.id,
  ct.category_id,
  ct.description,
  false,
  false,
  ct.position,
  ct.id,
  now(),
  now()
FROM checklists c
JOIN checklist_templates ct ON c.type = ct.type
WHERE c.type IN ('rack-preparation', 'engineering')
AND NOT EXISTS (
  SELECT 1 
  FROM checklist_items ci 
  WHERE ci.checklist_id = c.id 
  AND ci.template_id = ct.id
);

-- Ensure all categories used by templates have headers in the relevant checklists.
INSERT INTO checklist_items (
  id,
  checklist_id,
  category_id,
  description,
  completed,
  is_header,
  position,
  created_at,
  updated_at
)
SELECT 
  gen_random_uuid(),
  c.id,
  cat.id AS category_id,
  cat.name,
  false,
  true,
  0,
  now(),
  now()
FROM checklists c
CROSS JOIN checklist_categories cat
WHERE c.type IN ('rack-preparation', 'engineering')
AND cat.id IN (
  -- Get all categories used by templates for these checklist types
  SELECT DISTINCT category_id 
  FROM checklist_templates 
  WHERE type IN ('rack-preparation', 'engineering')
)
AND NOT EXISTS (
  SELECT 1 
  FROM checklist_items ci 
  WHERE ci.checklist_id = c.id 
  AND ci.category_id = cat.id 
  AND ci.is_header = true
);

-- Renumber positions sequentially within each category (headers first).
WITH ranked_items AS (
  SELECT 
    ci.id,
    ci.checklist_id,
    ci.category_id,
    ci.is_header,
    ROW_NUMBER() OVER (
      PARTITION BY ci.checklist_id, ci.category_id
      ORDER BY ci.is_header DESC, ci.position, ci.created_at
    ) - 1 AS new_position
  FROM checklist_items ci
  JOIN checklists c ON ci.checklist_id = c.id
  WHERE c.type IN ('rack-preparation', 'engineering')
)
UPDATE checklist_items ci
SET position = ri.new_position
FROM ranked_items ri
WHERE ci.id = ri.id
AND ci.position != ri.new_position; 