// Component that displays courthouse information with progress tracking

import { useNavigate } from 'react-router-dom';
import { MapPin, ChevronRight } from 'lucide-react';
import type { Database } from '../types/supabase';
import { cn } from '../lib/utils';

type Courthouse = Database['public']['Tables']['courthouses']['Row'];

// Props for the CourthouseListItem component
interface CourthouseListItemProps {
  courthouse: Courthouse;
}


export function CourthouseListItem({ courthouse }: CourthouseListItemProps) {
  const navigate = useNavigate();

  return (
    <div
      onClick={() => navigate(`/courthouse/${courthouse.id}`)}
      className="group hover:bg-gray-50 transition-colors duration-150 cursor-pointer"
    >
      <div className="p-4 flex items-center justify-between">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
            {courthouse.name}
          </h3>
          <div className="flex items-center space-x-2 text-gray-600 mt-1">
            <MapPin className="h-4 w-4 flex-shrink-0" />
            <span className="text-sm truncate">{courthouse.address}</span>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {/* Progress indicator */}
          <div className="text-right">
            <div className="text-sm font-medium text-gray-900">{courthouse.progress}%</div>
            <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
              <div
                className={cn(
                  "h-full rounded-full transition-all duration-300",
                  courthouse.progress >= 80 ? "bg-green-500" :
                  courthouse.progress >= 50 ? "bg-blue-500" :
                  courthouse.progress >= 20 ? "bg-yellow-500" : "bg-red-500"
                )}
                style={{ width: `${courthouse.progress}%` }}
              />
            </div>
          </div>

          {/* Arrow indicator on hover */}
          <ChevronRight className="h-5 w-5 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
        </div>
      </div>
    </div>
  );
}