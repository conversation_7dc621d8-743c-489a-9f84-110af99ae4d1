-- Migration to add <PERSON><PERSON><PERSON><PERSON> company and set up company-project relationship

-- Add JUDCO to the user_company enum type
ALTER TYPE user_company ADD VALUE IF NOT EXISTS 'JUDCO';

-- Create a table to manage company-project relationships
CREATE TABLE IF NOT EXISTS company_projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company user_company NOT NULL,
    project_id TEXT NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(company, project_id)
);

-- Add RLS policies to the company_projects table
ALTER TABLE company_projects ENABLE ROW LEVEL SECURITY;

-- Policy: Only admin can insert into company_projects
CREATE POLICY company_projects_insert_policy ON company_projects
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.jwt() ->> 'company' = 'admin');

CREATE POLICY company_projects_update_policy ON company_projects
    FOR UPDATE
    TO authenticated
    USING (auth.jwt() ->> 'company' = 'admin');

-- Policy: Only admin can delete from company_projects
CREATE POLICY company_projects_delete_policy ON company_projects
    FOR DELETE
    TO authenticated
    USING (auth.jwt() ->> 'company' = 'admin');

-- Policy: All authenticated users can select from company_projects
CREATE POLICY company_projects_select_policy ON company_projects
    FOR SELECT
    TO authenticated
    USING (true);

-- Add RLS policy to projects table to restrict access based on company
-- Drop any existing policy first
DROP POLICY IF EXISTS projects_select_policy ON projects;

-- New Policy: Allows access if user is admin, BEINCOURT (legacy), or their company is linked to the project
CREATE POLICY projects_select_policy ON projects
    FOR SELECT
    TO authenticated
    USING (
        auth.jwt() ->> 'company' = 'admin' OR
        auth.jwt() ->> 'company' = 'BEINCOURT' OR
        EXISTS (
            SELECT 1 FROM company_projects
            WHERE company_projects.project_id = projects.id
            AND company_projects.company = (auth.jwt() ->> 'company')::user_company
        )
    );

-- Add RLS policy to courthouses table based on company
DROP POLICY IF EXISTS courthouses_select_policy ON courthouses;

-- Policy: Allows access if user is admin, BEINCOURT (legacy), or their company is linked to the courthouse's project
CREATE POLICY courthouses_select_policy ON courthouses
    FOR SELECT
    TO authenticated
    USING (
        auth.jwt() ->> 'company' = 'admin' OR
        auth.jwt() ->> 'company' = 'BEINCOURT' OR
        EXISTS (
            SELECT 1 FROM company_projects
            WHERE company_projects.project_id = courthouses.project_id
            AND company_projects.company = (auth.jwt() ->> 'company')::user_company
        )
    );

-- Add RLS policy to departments table based on company
DROP POLICY IF EXISTS departments_select_policy ON departments;

-- Policy: Allows access if user is admin, BEINCOURT (legacy), or their company is linked to the department's project
CREATE POLICY departments_select_policy ON departments
    FOR SELECT
    TO authenticated
    USING (
        auth.jwt() ->> 'company' = 'admin' OR
        auth.jwt() ->> 'company' = 'BEINCOURT' OR
        EXISTS (
            SELECT 1 FROM company_projects
            JOIN courthouses ON courthouses.project_id = company_projects.project_id
            WHERE departments.courthouse_id = courthouses.id
            AND company_projects.company = (auth.jwt() ->> 'company')::user_company
        )
    );

GRANT ALL ON company_projects TO authenticated;
