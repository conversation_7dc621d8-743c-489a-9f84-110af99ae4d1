import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { DepartmentList } from '../components/DepartmentList';
import { getCourthouse, getDepartments } from '../lib/db';
import type { Database } from '../types/supabase';
import { Department } from '../types';
import { LoadingAnimation } from '../components/LoadingAnimation';
import { usePageLoading } from '../hooks/usePageLoading';
import { CreateDepartmentDialog } from '../components/CreateDepartmentDialog';
import { useUser } from '@/contexts/UserContext';

type Courthouse = Database['public']['Tables']['courthouses']['Row'];

export function DepartmentsPage() {
  const { courthouseId } = useParams();
  const { user } = useUser();
  const [courthouse, setCourthouse] = useState<Courthouse | null>(null);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const { loading, fadeOut } = usePageLoading(isInitialLoading);
  const [error, setError] = useState<string | null>(null);

  const loadData = async () => {
    if (!courthouseId) return;

    try {
      const [courthouseData, departmentsData] = await Promise.all([
        getCourthouse(courthouseId),
        getDepartments(courthouseId)
      ]);

      setCourthouse(courthouseData);
      setDepartments(departmentsData);
    } catch (err) {
      console.error('Error loading courthouse data:', err);
      setError('Failed to load courthouse data. Please try again later.');
    } finally {
      setIsInitialLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [courthouseId]);

  if (loading) {
    return <LoadingAnimation fadeOut={fadeOut} />;
  }

  if (error) {
    return (
      <div className="flex-1 p-6">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  if (!courthouse) {
    return (
      <div className="flex-1 p-6">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
          Courthouse not found
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 p-6">
      <div className="mb-8 text-center">
        <h2 className="text-2xl font-bold text-gray-800">{courthouse.name} Departments</h2>
        <p className="text-gray-600">
          {courthouse.address}
        </p>
        <div className="mt-4 flex flex-col sm:flex-row justify-center items-center gap-2">
          <div className="text-sm font-medium text-gray-600 whitespace-nowrap">Overall Progress:</div>
          <div className="w-full sm:w-32 h-2 bg-gray-200 rounded-full overflow-hidden">
            <div
              className="h-full rounded-full bg-blue-500 transition-all duration-300"
              style={{ width: `${courthouse.progress}%` }}
            />
          </div>
          <div className="text-sm font-medium text-gray-900">{courthouse.progress}%</div>
        </div>
      </div>

      {departments.length > 0 ? (
        <DepartmentList 
          departments={departments} 
          courthouseId={courthouseId || ''} 
          courthouseName={courthouse.name}
          onDepartmentCreated={loadData}
        />
      ) : (
        <div className="text-center">
          <p className="text-gray-500 mb-4">No departments found in this courthouse.</p>
          {user?.company === 'admin' && (
            <div className="flex justify-center">
              <CreateDepartmentDialog 
                courthouseId={courthouseId || ''}
                onDepartmentCreated={loadData}
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
} 