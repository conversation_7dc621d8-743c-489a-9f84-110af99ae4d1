-- Fixes checklist items appearing as duplicated category names.
-- Removes non-header items matching their category name, ensures headers exist, renumbers positions.

BEGIN;

-- Delete non-header tasks matching their category name
DELETE FROM checklist_items
WHERE is_header = false
  AND description = (
    SELECT name FROM checklist_categories
    WHERE checklist_categories.id = checklist_items.category_id
  )
  AND checklist_id IN (
    SELECT id FROM checklists
    WHERE type IN ('technicians', 'cvcs', 'advanced-staging', 'rack-preparation', 'engineering')
  );

-- Ensure a header item exists for each category that has non-header items
INSERT INTO checklist_items (id, checklist_id, category_id, description, is_header, position)
SELECT 
  CONCAT(ci.checklist_id, '_', ci.category_id, '_header_', md5(random()::text)) AS id,
  ci.checklist_id,
  ci.category_id,
  cc.name AS description,
  true AS is_header,
  0 AS position
FROM (
    SELECT DISTINCT checklist_id, category_id
    FROM checklist_items
    WHERE is_header = false
      AND checklist_id IN (
          SELECT id FROM checklists
          WH<PERSON><PERSON> type IN ('technicians', 'cvcs', 'advanced-staging', 'rack-preparation', 'engineering')
      )
) AS ci
JOIN checklist_categories cc ON ci.category_id = cc.id
LEFT JOIN checklist_items header ON header.checklist_id = ci.checklist_id AND header.category_id = ci.category_id AND header.is_header = true
WHERE header.id IS NULL;

-- Renumber item positions within each checklist and category (headers first)
WITH ordered_items AS (
  SELECT
    ci.id,
    ci.checklist_id,
    ci.category_id,
    ROW_NUMBER() OVER (
      PARTITION BY ci.checklist_id, ci.category_id
      ORDER BY ci.is_header DESC, ci.position NULLS LAST, ci.description
    ) - 1 AS new_position
  FROM checklist_items ci
  WHERE checklist_id IN (
    SELECT id FROM checklists
    WHERE type IN ('technicians', 'cvcs', 'advanced-staging', 'rack-preparation', 'engineering')
  )
)
UPDATE checklist_items
SET position = ordered_items.new_position
FROM ordered_items
WHERE checklist_items.id = ordered_items.id;

COMMIT; 