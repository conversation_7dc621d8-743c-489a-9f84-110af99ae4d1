<!DOCTYPE html><html><head>
      <title>prd</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      
      <link rel="stylesheet" href="file:///c:\Users\<USER>\.cursor\extensions\shd101wyy.markdown-preview-enhanced-0.8.18\crossnote\dependencies\katex\katex.min.css">
      
      
      
      
      
      <style>
      code[class*=language-],pre[class*=language-]{color:#333;background:0 0;font-family:<PERSON>solas,"Liberation Mono",Menlo,Courier,monospace;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.4;-moz-tab-size:8;-o-tab-size:8;tab-size:8;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:.8em;overflow:auto;border-radius:3px;background:#f5f5f5}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal;background:#f5f5f5}.token.blockquote,.token.comment{color:#969896}.token.cdata{color:#183691}.token.doctype,.token.macro.property,.token.punctuation,.token.variable{color:#333}.token.builtin,.token.important,.token.keyword,.token.operator,.token.rule{color:#a71d5d}.token.attr-value,.token.regex,.token.string,.token.url{color:#183691}.token.atrule,.token.boolean,.token.code,.token.command,.token.constant,.token.entity,.token.number,.token.property,.token.symbol{color:#0086b3}.token.prolog,.token.selector,.token.tag{color:#63a35c}.token.attr-name,.token.class,.token.class-name,.token.function,.token.id,.token.namespace,.token.pseudo-class,.token.pseudo-element,.token.url-reference .token.variable{color:#795da3}.token.entity{cursor:help}.token.title,.token.title .token.punctuation{font-weight:700;color:#1d3e81}.token.list{color:#ed6a43}.token.inserted{background-color:#eaffea;color:#55a532}.token.deleted{background-color:#ffecec;color:#bd2c00}.token.bold{font-weight:700}.token.italic{font-style:italic}.language-json .token.property{color:#183691}.language-markup .token.tag .token.punctuation{color:#333}.language-css .token.function,code.language-css{color:#0086b3}.language-yaml .token.atrule{color:#63a35c}code.language-yaml{color:#183691}.language-ruby .token.function{color:#333}.language-markdown .token.url{color:#795da3}.language-makefile .token.symbol{color:#795da3}.language-makefile .token.variable{color:#183691}.language-makefile .token.builtin{color:#0086b3}.language-bash .token.keyword{color:#0086b3}pre[data-line]{position:relative;padding:1em 0 1em 3em}pre[data-line] .line-highlight-wrapper{position:absolute;top:0;left:0;background-color:transparent;display:block;width:100%}pre[data-line] .line-highlight{position:absolute;left:0;right:0;padding:inherit 0;margin-top:1em;background:hsla(24,20%,50%,.08);background:linear-gradient(to right,hsla(24,20%,50%,.1) 70%,hsla(24,20%,50%,0));pointer-events:none;line-height:inherit;white-space:pre}pre[data-line] .line-highlight:before,pre[data-line] .line-highlight[data-end]:after{content:attr(data-start);position:absolute;top:.4em;left:.6em;min-width:1em;padding:0 .5em;background-color:hsla(24,20%,50%,.4);color:#f4f1ef;font:bold 65%/1.5 sans-serif;text-align:center;vertical-align:.3em;border-radius:999px;text-shadow:none;box-shadow:0 1px #fff}pre[data-line] .line-highlight[data-end]:after{content:attr(data-end);top:auto;bottom:.4em}html body{font-family:'Helvetica Neue',Helvetica,'Segoe UI',Arial,freesans,sans-serif;font-size:16px;line-height:1.6;color:#333;background-color:#fff;overflow:initial;box-sizing:border-box;word-wrap:break-word}html body>:first-child{margin-top:0}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{line-height:1.2;margin-top:1em;margin-bottom:16px;color:#000}html body h1{font-size:2.25em;font-weight:300;padding-bottom:.3em}html body h2{font-size:1.75em;font-weight:400;padding-bottom:.3em}html body h3{font-size:1.5em;font-weight:500}html body h4{font-size:1.25em;font-weight:600}html body h5{font-size:1.1em;font-weight:600}html body h6{font-size:1em;font-weight:600}html body h1,html body h2,html body h3,html body h4,html body h5{font-weight:600}html body h5{font-size:1em}html body h6{color:#5c5c5c}html body strong{color:#000}html body del{color:#5c5c5c}html body a:not([href]){color:inherit;text-decoration:none}html body a{color:#08c;text-decoration:none}html body a:hover{color:#00a3f5;text-decoration:none}html body img{max-width:100%}html body>p{margin-top:0;margin-bottom:16px;word-wrap:break-word}html body>ol,html body>ul{margin-bottom:16px}html body ol,html body ul{padding-left:2em}html body ol.no-list,html body ul.no-list{padding:0;list-style-type:none}html body ol ol,html body ol ul,html body ul ol,html body ul ul{margin-top:0;margin-bottom:0}html body li{margin-bottom:0}html body li.task-list-item{list-style:none}html body li>p{margin-top:0;margin-bottom:0}html body .task-list-item-checkbox{margin:0 .2em .25em -1.8em;vertical-align:middle}html body .task-list-item-checkbox:hover{cursor:pointer}html body blockquote{margin:16px 0;font-size:inherit;padding:0 15px;color:#5c5c5c;background-color:#f0f0f0;border-left:4px solid #d6d6d6}html body blockquote>:first-child{margin-top:0}html body blockquote>:last-child{margin-bottom:0}html body hr{height:4px;margin:32px 0;background-color:#d6d6d6;border:0 none}html body table{margin:10px 0 15px 0;border-collapse:collapse;border-spacing:0;display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all}html body table th{font-weight:700;color:#000}html body table td,html body table th{border:1px solid #d6d6d6;padding:6px 13px}html body dl{padding:0}html body dl dt{padding:0;margin-top:16px;font-size:1em;font-style:italic;font-weight:700}html body dl dd{padding:0 16px;margin-bottom:16px}html body code{font-family:Menlo,Monaco,Consolas,'Courier New',monospace;font-size:.85em;color:#000;background-color:#f0f0f0;border-radius:3px;padding:.2em 0}html body code::after,html body code::before{letter-spacing:-.2em;content:'\00a0'}html body pre>code{padding:0;margin:0;word-break:normal;white-space:pre;background:0 0;border:0}html body .highlight{margin-bottom:16px}html body .highlight pre,html body pre{padding:1em;overflow:auto;line-height:1.45;border:#d6d6d6;border-radius:3px}html body .highlight pre{margin-bottom:0;word-break:normal}html body pre code,html body pre tt{display:inline;max-width:initial;padding:0;margin:0;overflow:initial;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}html body pre code:after,html body pre code:before,html body pre tt:after,html body pre tt:before{content:normal}html body blockquote,html body dl,html body ol,html body p,html body pre,html body ul{margin-top:0;margin-bottom:16px}html body kbd{color:#000;border:1px solid #d6d6d6;border-bottom:2px solid #c7c7c7;padding:2px 4px;background-color:#f0f0f0;border-radius:3px}@media print{html body{background-color:#fff}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{color:#000;page-break-after:avoid}html body blockquote{color:#5c5c5c}html body pre{page-break-inside:avoid}html body table{display:table}html body img{display:block;max-width:100%;max-height:100%}html body code,html body pre{word-wrap:break-word;white-space:pre}}.markdown-preview{width:100%;height:100%;box-sizing:border-box}.markdown-preview ul{list-style:disc}.markdown-preview ul ul{list-style:circle}.markdown-preview ul ul ul{list-style:square}.markdown-preview ol{list-style:decimal}.markdown-preview ol ol,.markdown-preview ul ol{list-style-type:lower-roman}.markdown-preview ol ol ol,.markdown-preview ol ul ol,.markdown-preview ul ol ol,.markdown-preview ul ul ol{list-style-type:lower-alpha}.markdown-preview .newpage,.markdown-preview .pagebreak{page-break-before:always}.markdown-preview pre.line-numbers{position:relative;padding-left:3.8em;counter-reset:linenumber}.markdown-preview pre.line-numbers>code{position:relative}.markdown-preview pre.line-numbers .line-numbers-rows{position:absolute;pointer-events:none;top:1em;font-size:100%;left:0;width:3em;letter-spacing:-1px;border-right:1px solid #999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.markdown-preview pre.line-numbers .line-numbers-rows>span{pointer-events:none;display:block;counter-increment:linenumber}.markdown-preview pre.line-numbers .line-numbers-rows>span:before{content:counter(linenumber);color:#999;display:block;padding-right:.8em;text-align:right}.markdown-preview .mathjax-exps .MathJax_Display{text-align:center!important}.markdown-preview:not([data-for=preview]) .code-chunk .code-chunk-btn-group{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .status{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .output-div{margin-bottom:16px}.markdown-preview .md-toc{padding:0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link div,.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}.markdown-preview .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}.scrollbar-style::-webkit-scrollbar{width:8px}.scrollbar-style::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}.scrollbar-style::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode]){position:relative;width:100%;height:100%;top:0;left:0;margin:0;padding:0;overflow:auto}html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{position:relative;top:0;min-height:100vh}@media screen and (min-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em calc(50% - 457px + 2em)}}@media screen and (max-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{font-size:14px!important;padding:1em}}@media print{html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{display:none}}html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{position:fixed;bottom:8px;left:8px;font-size:28px;cursor:pointer;color:inherit;z-index:99;width:32px;text-align:center;opacity:.4}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] #sidebar-toc-btn{opacity:1}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc{position:fixed;top:0;left:0;width:300px;height:100%;padding:32px 0 48px 0;font-size:14px;box-shadow:0 0 4px rgba(150,150,150,.33);box-sizing:border-box;overflow:auto;background-color:inherit}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar{width:8px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc a{text-decoration:none}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc{padding:0 16px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link div,html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{left:300px;width:calc(100% - 300px);padding:2em calc(50% - 457px - 300px / 2);margin:0;box-sizing:border-box}@media screen and (max-width:1274px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{width:100%}}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .markdown-preview{left:50%;transform:translateX(-50%)}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .md-sidebar-toc{display:none}
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */

      </style>
      <!-- The content below will be included at the end of the <head> element. --><script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function () {
    // your code here
  });
</script></head><body for="html-export">
    
    
      <div class="crossnote markdown-preview  ">
      <div>
<h1 id="product-requirements-document-bic-dashboard">Product Requirements Document: BIC Dashboard </h1>
<h2 id="1-introduction">1. Introduction </h2>
<ul>
<li><strong>Purpose</strong>: This document lays out what's needed for the BIC Dashboard project. By building a React/TypeScript web app with Vite to help manage projects within BIC, track progress across different departments, handle checklists, log issues, and provide real-time updates.</li>
<li><strong>Vision</strong>: This application aims to give BIC engineers, technicians, and project managers a straightforward, easy-to-use web interface. It should help them keep an eye on project progress, department status, and task completion while providing tools for issue tracking and collaboration.</li>
<li><strong>Scope</strong>: This version focuses on the core project management and tracking features. By using React's component architecture, Supabase for backend and real-time updates, the Context API for state management, custom hooks for business logic, and React Router for navigation.</li>
</ul>
<h2 id="2-goals">2. Goals </h2>
<p><em>Here's the steps to achieve the BIC Dashboard.</em></p>
<ul>
<li><strong>Goal 1</strong>: Create a central dashboard where BIC employees can track and manage projects, departments, and tasks. The aim is to cut down the time spent on manual tracking and status updates by about 40% within six months after launch.</li>
<li><strong>Goal 2</strong>: Make project and department status easier to see and understand for everyone involved, using clear visuals and good filtering. This should help teams make quicker, smarter decisions about project management.</li>
<li><strong>Goal 3</strong>: Build a solid React/TypeScript base with Supabase integration that can easily be added to later (like new data sources or more advanced analytics) without slowing things down. Core interactions should take less than 2 seconds.</li>
</ul>
<h2 id="3-target-audience">3. Target Audience </h2>
<p><em>Who's this dashboard for?</em></p>
<ul>
<li><strong>Primary Users</strong>: BIC engineers, technicians, and project managers. These are the people who need to regularly check project status, complete checklists, log issues, and monitor department progress for their day-to-day work.</li>
<li><strong>Secondary Users</strong>: Executive leadership and other project managers from partner companies (BEINCOURT, CVCS, JUDCO). They're looking for high-level summaries, project progress indicators, and issue logs for big-picture decisions and oversight.</li>
</ul>
<h2 id="4-functional-requirements-using-moscow-method">4. Functional Requirements (Using MoSCoW Method) </h2>
<p><em>Here are the specific features the dashboard needs, broken down by priority (Must Have, Should Have, Could Have, Won't Have).</em></p>
<h3 id="must-have-m---core-stuff-needed-for-launch">Must Have (M) - Core Stuff Needed for Launch </h3>
<p><em>These features are absolutely essential for this version.</em></p>
<ul>
<li>
<p><strong>Feature 1: Authentication</strong></p>
<ul>
<li><strong>Description:</strong> Secure login/logout and company-based access control.</li>
<li><strong>Details:</strong> Users log in with their BIC credentials via Supabase authentication. Different companies (BEINCOURT, CVCS, JUDCO, admin) see different projects and have different access levels.</li>
<li><strong>Implementation Tasks:</strong>
<ul>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Implement Supabase authentication for user management.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Create <code>UserContext</code> and <code>useUser</code> hook for auth state (user info, company, session).</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Build login page with username/password authentication.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Implement registration with access key protection.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Implement logout functionality.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Develop <code>ProtectedRoute</code> to restrict access based on auth status.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Store user profile info (username, company) in <code>UserContext</code>.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Implement company-based access control for projects.</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>Feature 2: Project Management</strong></p>
<ul>
<li><strong>Description:</strong> The main dashboard showing all accessible projects with status and progress.</li>
<li><strong>Details:</strong> The homepage displays projects with progress indicators, filtering by company access rules (JUDCO users only see San Diego project, etc.).</li>
<li><strong>Implementation Tasks:</strong>
<ul>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Design dashboard homepage layout with project cards (React components, Tailwind).</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Create project data structure and database schema in Supabase.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Implement project filtering based on user's company.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Create a <code>ProjectList</code> component with search functionality.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Develop data fetching hooks for projects with loading/error states.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Add visual progress indicators for project completion percentage.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Implement loading state indicators for initial data load.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Add error handling for failed data fetches.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Ensure responsive layout for desktop/laptop screens.</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>Feature 3: Department Management</strong></p>
<ul>
<li><strong>Description:</strong> View and manage departments within courthouses/projects.</li>
<li><strong>Details:</strong> Users can see all departments in a courthouse, filter by status, and access detailed department views.</li>
<li><strong>Implementation Tasks:</strong>
<ul>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Create department data structure and database schema.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Implement department listing page with filtering options.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Add status indicators (on-track, delayed, complete) for departments.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Create progress visualization for department completion.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Implement search functionality for departments.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Add filtering by status (all, incomplete, in-progress, complete, open-issues).</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Create department detail page with all associated checklists.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Implement birds-eye view for department overview.</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>Feature 4: Checklist Management</strong></p>
<ul>
<li><strong>Description:</strong> Track and update task checklists for departments.</li>
<li><strong>Details:</strong> Different checklist types (technicians, UAT, CVCS, advanced-staging, rack-preparation, engineering) with items that can be checked off and annotated.</li>
<li><strong>Implementation Tasks:</strong>
<ul>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Design checklist data structure with categories and items.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Create checklist components for displaying and interacting with tasks.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Implement item completion tracking with user attribution.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Add timestamp recording for completed items.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Create progress calculation for checklist completion.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Implement access control for checklist item completion.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Add note functionality for checklist items.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Create checklist management page for administration.</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>Feature 5: Issue Tracking</strong></p>
<ul>
<li><strong>Description:</strong> Log and track issues for departments and checklist items.</li>
<li><strong>Details:</strong> Users can flag issues, assign status (Open, In Progress, Resolved), and track them across the system.</li>
<li><strong>Implementation Tasks:</strong>
<ul>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Create issue data structure and database schema.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Implement issue creation from department and checklist views.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Add issue status management (Open, In Progress, Resolved).</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Create issue log page with filtering by status.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Implement issue detail view with status updates.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Add visual indicators for departments with open issues.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Create issue count badges for quick reference.</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>Feature 6: Navigation</strong></p>
<ul>
<li><strong>Description:</strong> Easy-to-use multi-page navigation using client-side routing.</li>
<li><strong>Details:</strong> Users can move between projects, courthouses, departments, and system pages using a persistent nav bar.</li>
<li><strong>Implementation Tasks:</strong>
<ul>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Set up <code>react-router-dom</code> for client-side routing.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Design and implement main <code>Layout</code> component with header navigation.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Define routes for all primary sections.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Create back button functionality for nested navigation.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Implement user profile dropdown in navigation.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Add mobile-responsive menu toggle.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Clearly indicate active navigation state.</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>Feature 7: Real-time Updates</strong></p>
<ul>
<li><strong>Description:</strong> Live data updates without page refreshes.</li>
<li><strong>Details:</strong> Changes to checklists, issues, and department status update in real-time across all connected clients.</li>
<li><strong>Implementation Tasks:</strong>
<ul>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Implement Supabase real-time subscriptions.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Create real-time hooks for checklist updates.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Add real-time issue status tracking.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Implement optimistic UI updates for better user experience.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Add visual indicators for real-time changes.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Ensure proper error handling for subscription failures.</li>
</ul>
</li>
</ul>
</li>
</ul>
<h3 id="should-have-s---important-but-not-essential-for-launch">Should Have (S) - Important, but Not Essential for Launch </h3>
<p><em>These features would add a lot of value, but it can launch without them if needed.</em></p>
<ul>
<li>
<p><strong>Feature 1: Advanced Filtering and Search</strong></p>
<ul>
<li><strong>Description:</strong> Enhanced filtering and search capabilities across the application.</li>
<li><strong>Details:</strong> More advanced filtering options for projects, departments, and issues, with saved filter preferences.</li>
<li><strong>Implementation Tasks:</strong>
<ul>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Implement basic search for projects and departments.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Add status filtering for departments (all, incomplete, in-progress, complete, open-issues).</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Create issue filtering by status (Open, In Progress, Resolved, All).</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Add date range filtering for issues and completed tasks.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Implement saved filter preferences in localStorage.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Create advanced search with multiple criteria.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Add filter history for quick access to previous searches.</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>Feature 2: Data Visualization</strong></p>
<ul>
<li><strong>Description:</strong> Visual representations of project and department progress.</li>
<li><strong>Details:</strong> Charts and graphs showing completion rates, issue trends, and department status.</li>
<li><strong>Implementation Tasks:</strong>
<ul>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Implement progress bars for project and department completion.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Create visual status indicators (color coding for on-track, delayed, complete).</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Add birds-eye view visualization for department overview.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Create trend charts for issue resolution over time.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Implement completion rate charts for departments and projects.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Add tooltips for detailed information on hover.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Ensure visualizations are consistent with the application theme.</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>Feature 3: Data Export</strong></p>
<ul>
<li><strong>Description:</strong> Export project, department, and issue data for offline analysis.</li>
<li><strong>Details:</strong> Allow users to export data to CSV format for further processing or reporting.</li>
<li><strong>Implementation Tasks:</strong>
<ul>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Create utility functions for CSV export.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Add export buttons to project list, department list, and issue log.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Implement data formatting for export.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Add export options (all data vs. filtered data).</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Handle large data exports efficiently.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Provide feedback during export process.</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>Feature 4: Theme Support</strong></p>
<ul>
<li><strong>Description:</strong> Light and dark mode theme options.</li>
<li><strong>Details:</strong> Allow users to switch between light and dark color schemes based on preference.</li>
<li><strong>Implementation Tasks:</strong>
<ul>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Configure Tailwind for dark mode support.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Create theme toggle component in user profile dropdown.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Implement theme state management with Context API.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Store theme preference in localStorage.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Ensure all components respect the selected theme.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Add system preference detection for automatic theme selection.</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>Feature 5: User Profile Management</strong></p>
<ul>
<li><strong>Description:</strong> Enhanced user profile features and settings.</li>
<li><strong>Details:</strong> Allow users to update profile information, change passwords, and manage preferences.</li>
<li><strong>Implementation Tasks:</strong>
<ul>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Implement basic user profile display in dropdown.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Add password change functionality.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Create user settings page.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Add profile picture upload capability.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Implement notification preferences.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Add user activity history.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Create user preference management interface.</li>
</ul>
</li>
</ul>
</li>
</ul>
<h3 id="could-have-c---nice-to-have-less-critical">Could Have (C) - Nice to Have, Less Critical </h3>
<p><em>These would be good additions if they don't delay higher priority items.</em></p>
<ul>
<li>
<p><strong>Feature 1: Advanced Notifications</strong></p>
<ul>
<li><strong>Description:</strong> In-app notification system for important events and updates.</li>
<li><strong>Details:</strong> Notify users about issue status changes, checklist completions, or new assignments.</li>
<li><strong>Implementation Tasks:</strong>
<ul>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Design notification components (toast, banner, badge).</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Implement notification storage in database.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Create real-time notification delivery using Supabase.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Add notification center in user interface.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Implement notification preferences and settings.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Add read/unread status tracking.</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>Feature 2: Customizable Dashboard</strong></p>
<ul>
<li><strong>Description:</strong> Allow users to personalize their dashboard view.</li>
<li><strong>Details:</strong> Drag-and-drop interface for reordering projects, custom filters, and saved views.</li>
<li><strong>Implementation Tasks:</strong>
<ul>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Implement drag-and-drop with DND Kit for project cards.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Create saved view functionality for dashboard layouts.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Add customizable widgets for different data views.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Store user's layout preferences in database.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Create dashboard settings panel.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Add reset to default option.</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>Feature 3: Advanced Reporting</strong></p>
<ul>
<li><strong>Description:</strong> Generate detailed reports on project and department status.</li>
<li><strong>Details:</strong> Create customizable reports with various metrics and visualizations.</li>
<li><strong>Implementation Tasks:</strong>
<ul>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Design report templates for different data types.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Create report builder interface.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Implement PDF export functionality.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Add scheduled report generation.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Create report sharing capabilities.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Implement report history and archiving.</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>Feature 4: Task Assignment and Tracking</strong></p>
<ul>
<li><strong>Description:</strong> Assign checklist items and issues to specific users.</li>
<li><strong>Details:</strong> Track who is responsible for completing tasks and resolving issues.</li>
<li><strong>Implementation Tasks:</strong>
<ul>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Track who completed checklist items and when.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Add explicit task assignment functionality.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Implement task due dates and reminders.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Create user workload view.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Add task priority levels.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Implement task dependencies.</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>Feature 5: Collaboration Tools</strong></p>
<ul>
<li><strong>Description:</strong> Enhanced collaboration features for team communication.</li>
<li><strong>Details:</strong> Comments, mentions, and discussion threads for issues and tasks.</li>
<li><strong>Implementation Tasks:</strong>
<ul>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Basic notes functionality for checklist items.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Implement threaded comments on issues.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Add @mentions for user notifications.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Create activity feeds for departments and projects.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Implement file attachments for issues and comments.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Add real-time comment updates.</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>Feature 6: Mobile Optimization</strong></p>
<ul>
<li><strong>Description:</strong> Enhanced mobile experience beyond basic responsiveness.</li>
<li><strong>Details:</strong> Optimized mobile layouts and touch interactions for field use.</li>
<li><strong>Implementation Tasks:</strong>
<ul>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked=""> Basic responsive design for core functionality.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Create mobile-optimized checklist interfaces.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Implement touch-friendly controls for field use.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Add offline capability for checklist completion.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Optimize performance for mobile networks.</li>
<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox"> Create mobile-specific navigation patterns.</li>
</ul>
</li>
</ul>
</li>
</ul>
<h3 id="wont-have-w---not-in-this-version">Won't Have (W) - Not in this version </h3>
<p><em>To keep focus and deliver core value on time, these features are out of scope for this version:</em></p>
<ul>
<li><strong>Native Mobile App</strong>: No dedicated iOS/Android app. Focus on responsive web design instead.</li>
<li><strong>Analytics</strong>: No insights or predictive analytics.</li>
<li><strong>Multi-Language Support</strong>: UI will be English-only for this version.</li>
<li><strong>Server-Side Rendering (SSR)</strong>: This version will remain a Client-Side Rendered (CSR) app.</li>
<li><strong>Third-party Integrations</strong>: Limited to Supabase for backend. No other third-party service integrations.</li>
<li><strong>Automated Reporting/Emailing</strong>: No automated generation or email delivery of reports.</li>
<li><strong>Advanced User Management</strong>: User creation and role assignment handled through admin interface only.</li>
<li><strong>Public API</strong>: No public API for third-party integrations in this version.</li>
<li><strong>Advanced Analytics</strong>: No complex data analysis or business intelligence features.</li>
<li><strong>Gantt Charts/Timeline Views</strong>: No timeline-based project visualization in initial version.</li>
</ul>
<h2 id="5-non-functional-requirements">5. Non-Functional Requirements </h2>
<p><em>These are about the quality and operation of the system.</em></p>
<ul>
<li><strong>Performance</strong>:
<ul>
<li>Initial dashboard load (LCP) under 2.5 seconds on a standard corporate network.</li>
<li>Real-time updates should be delivered within 1 second of changes.</li>
<li>Checklist interactions (checking items, adding notes) should feel immediate.</li>
<li>Progress calculations and status updates should be efficient.</li>
<li>Optimize Supabase queries and real-time subscriptions.</li>
<li>Implement optimistic UI updates for better perceived performance.</li>
<li>Optimize app bundle size (code splitting, tree shaking).</li>
</ul>
</li>
<li><strong>Scalability</strong>:
<ul>
<li>Database schema designed to handle hundreds of projects and thousands of departments.</li>
<li>Frontend architecture (React components, state management) must support adding new features without major refactoring.</li>
<li>Real-time subscription management to prevent connection limits.</li>
<li>Efficient data fetching with pagination for large data sets.</li>
<li>State management (Context API) organized to prevent performance bottlenecks.</li>
</ul>
</li>
<li><strong>Usability</strong>:
<ul>
<li>Intuitive interface requiring minimal training for BIC staff and contractors.</li>
<li>Consistent UI patterns for navigation, filtering, and checklist interactions.</li>
<li>Clear visual indicators for status, progress, and issues.</li>
<li>Responsive design optimized for desktop/laptop with basic mobile support.</li>
<li>Efficient workflows for common tasks (completing checklist items, logging issues).</li>
<li>WCAG complient.</li>
</ul>
</li>
<li><strong>Security</strong>:
<ul>
<li>All communication via HTTPS.</li>
<li>Secure authentication through Supabase.</li>
<li>Row-level security policies in database.</li>
<li>Company-based access control for projects and data.</li>
<li>Secure password storage and management.</li>
<li>Protection against common web vulnerabilities (XSS, CSRF).</li>
<li>Input validation for all user inputs.</li>
</ul>
</li>
<li><strong>Maintainability</strong>:
<ul>
<li>TypeScript throughout for type safety.</li>
<li>Consistent code style (ESLint, Prettier).</li>
<li>Modular architecture with clear separation of concerns.</li>
<li>Component-based design with reusable UI elements.</li>
<li>Well-organized project structure.</li>
<li>Clear comments for complex logic.</li>
<li>Version control with Git.</li>
</ul>
</li>
<li><strong>Reliability</strong>:
<ul>
<li>Graceful error handling for API failures with informative messages.</li>
<li>Offline data persistence where possible.</li>
<li>Data validation to prevent corruption.</li>
<li>Automatic reconnection for real-time subscriptions.</li>
<li>Cross-browser compatibility (latest versions of Chrome, Firefox, Safari, Edge).</li>
<li>Regular database backups.</li>
</ul>
</li>
</ul>
<h2 id="6-technical-architecture-high-level">6. Technical Architecture (High-Level) </h2>
<p><em>A quick look at the main tech and architectural choices.</em></p>
<ul>
<li><strong>Frontend Framework</strong>: React (v18+) with TypeScript.</li>
<li><strong>Build Tool</strong>: Vite.</li>
<li><strong>Backend &amp; Database</strong>: Supabase (PostgreSQL database, authentication, real-time subscriptions).</li>
<li><strong>State Management</strong>: React Context API with custom hooks for global state (User, Filters). Component state with <code>useState</code>/<code>useReducer</code>.</li>
<li><strong>Routing</strong>: <code>react-router-dom</code> (v6+).</li>
<li><strong>Data Fetching</strong>: Custom hooks using Supabase client for database operations and real-time subscriptions.</li>
<li><strong>UI Components</strong>: Custom React components and Shadcn UI, styled with Tailwind CSS.</li>
<li><strong>Styling</strong>: Tailwind CSS for utility-first styling with custom theme configuration.</li>
<li><strong>Animation</strong>: Framer Motion for transitions and interactive elements.</li>
<li><strong>Forms</strong>: React Hook Form with Zod for validation.</li>
<li><strong>Drag &amp; Drop</strong>: DND Kit for sortable interfaces.</li>
<li><strong>Date Handling</strong>: date-fns for date manipulation and formatting.</li>
<li><strong>Linting/Formatting</strong>: ESLint and Prettier.</li>
<li><strong>Testing</strong>: Jest and React Testing Library for unit/integration tests.</li>
</ul>
<h2 id="7-design-considerations">7. Design Considerations </h2>
<p><em>Guiding principles for UI/UX.</em></p>
<ul>
<li><strong>UI/UX</strong>: Clean, professional interface with a focus on usability for project management. Navy blue primary color scheme reflecting BIC's branding.</li>
<li><strong>Component Hierarchy</strong>: Organized around pages (Dashboard, ProjectPage, DepartmentDetailsPage), layout components (Layout, Navigation), feature components (ProjectList, DepartmentList, ChecklistPanel), and reusable UI components from Shadcn UI.</li>
<li><strong>Design System</strong>: Consistent color scheme, typography, and spacing via Tailwind CSS with custom theme configuration. Dark mode support in the CSS variables but not yet implemented in the UI.</li>
<li><strong>Accessibility</strong>: Basic accessibility considerations including color contrast, keyboard navigation, and semantic HTML.</li>
<li><strong>Responsive Design</strong>: Primary focus on desktop/laptop with basic mobile support through responsive breakpoints.</li>
</ul>
<h2 id="8-open-issues--questions">8. Open Issues / Questions </h2>
<p><em>Things that still need to be figured out.</em></p>
<ul>
<li>How to handle very large projects with hundreds of departments? Consider pagination or virtualized lists.</li>
<li>Best approach for implementing advanced filtering across multiple data types?</li>
<li>Strategy for offline support in areas with poor connectivity?</li>
<li>Performance optimization for real-time subscriptions with many concurrent users?</li>
<li>Backup and disaster recovery procedures for Supabase database?</li>
<li>User onboarding process and documentation needs?</li>
<li>Approach for implementing theme support with existing Shadcn UI components?</li>
<li>Requirements for data export formats and frequency?</li>
<li>Strategy for implementing advanced reporting features?</li>
<li>Deployment and CI/CD pipeline configuration?</li>
</ul>
<h2 id="9-future-considerations">9. Future Considerations </h2>
<p><em>Ideas for later versions, beyond this version.</em></p>
<ul>
<li>Advanced analytics and reporting with visualization dashboards.</li>
<li>Enhanced mobile experience with offline support for field use.</li>
<li>Integration with third-party project management tools.</li>
<li>Automated notifications via email or push notifications.</li>
<li>Enhanced collaboration features with comments and @mentions.</li>
<li>Timeline views and Gantt charts for project scheduling.</li>
<li>Document management and file attachments for departments and issues.</li>
<li>Advanced user management with fine-grained permissions.</li>
<li>Multi-language support for international teams.</li>
<li>Integration with IoT sensors for automated status updates.</li>
<li>Public API for third-party integrations.</li>
<li>Enhanced security features like two-factor authentication.</li>
<li>Audit logging for compliance and accountability.</li>
</ul>
<hr>
<p><em>Document Version: 7.0</em><br>
<em>Last Updated: May 1, 2025</em></p>
</div>
      </div>
      
      
    
    
    
    
    
    
  
    </body></html>