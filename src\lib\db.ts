import { supabase } from './supabase';
import type { Database } from '../types/supabase';
import { createClient } from '@supabase/supabase-js';
import type { Issue } from '../types/issues';

type Tables = Database['public']['Tables'];

class ErrorWithContext extends Error {
  constructor(message: string, public context: Record<string, unknown>) {
    super(message);
    this.name = 'ErrorWithContext';
  }
}

// Project operations
export async function getProjects() {
  // The RLS policies will automatically filter projects based on the user's company
  const { data, error } = await supabase
    .from('projects')
    .select('*')
    .order('name');

  if (error) throw error;
  return data;
}

export async function getProject(id: string) {
  const { data, error } = await supabase
    .from('projects')
    .select('*')
    .eq('id', id)
    .single();

  if (error) throw error;
  return data;
}

// Courthouse operations
export async function getCourthouses(projectId?: string) {
  let query = supabase.from('courthouses').select('*');

  if (projectId) {
    query = query.eq('project_id', projectId);
  }

  const { data, error } = await query.order('progress', { ascending: false });

  if (error) throw error;
  return data;
}

export async function createCourthouse(data: {
  name: string;
  address: string;
  projectId: string;
}) {
  // Create a slug from the courthouse name for the ID using the slugify helper function
  const courthouseSlug = slugify(data.name);

  // Check if this ID already exists to avoid conflicts
  const { data: existingCourthouse, error: idCheckError } = await supabase
    .from('courthouses')
    .select('id')
    .eq('id', courthouseSlug)
    .maybeSingle();

  if (idCheckError) {
    throw idCheckError;
  }

  // If ID already exists, add a sequential number suffix to ensure uniqueness
  let finalCourthouseId = courthouseSlug;

  if (existingCourthouse) {
    // Find all courthouses with similar IDs to determine the next number
    const { data: similarCourthouses, error: similarError } = await supabase
      .from('courthouses')
      .select('id')
      .like('id', `${courthouseSlug}-%`);

    if (similarError) {
      throw similarError;
    }

    // Find the highest number suffix and increment it
    let highestSuffix = 0;
    if (similarCourthouses && similarCourthouses.length > 0) {
      similarCourthouses.forEach(courthouse => {
        const match = courthouse.id.match(new RegExp(`^${courthouseSlug}-(\d+)$`));
        if (match && match[1]) {
          const suffix = parseInt(match[1], 10);
          if (suffix > highestSuffix) {
            highestSuffix = suffix;
          }
        }
      });
    }

    finalCourthouseId = `${courthouseSlug}-${highestSuffix + 1}`;
  }

  const { data: courthouse, error } = await supabase
    .from('courthouses')
    .insert({
      id: finalCourthouseId,
      name: data.name,
      address: data.address,
      project_id: data.projectId,
      progress: 0,
      status: 'on-track'
    })
    .select()
    .single();

  if (error) throw error;
  return courthouse;
}

export async function getCourthouse(id: string) {
  const { data, error } = await supabase
    .from('courthouses')
    .select('*')
    .eq('id', id)
    .single();

  if (error) throw error;
  return data;
}

// Department operations
export async function getDepartments(courthouseId: string) {
  const { data, error } = await supabase
    .from('departments')
    .select(`
      *,
      checklists (
        *,
        checklist_items (
          id,
          description,
          completed,
          completed_by_username,
          completed_at,
          is_header,
          checklist_categories (*),
          checklist_item_notes (
            id,
            content,
            created_by_username,
            created_at
          )
        )
      ),
      department_issues (
        id,
        title,
        content,
        status,
        created_by_username,
        created_at,
        task_id,
        task_description,
        checklist_type,
        category_name,
        department_id
      )
    `)
    .eq('courthouse_id', courthouseId)
    .order('progress', { ascending: false });

  if (error) throw error;

  // Transform the data to match our expected structure
  const transformedData = data?.map(department => ({
    ...department,
    checklists: department.checklists.map((checklist: any) => ({
      ...checklist,
      checklist_items: checklist.checklist_items.map((item: any) => ({
        ...item,
        notes: Array.isArray(item.checklist_item_notes) ? item.checklist_item_notes : []
      }))
    })),
    issues: Array.isArray(department.department_issues) ? department.department_issues : []
  }));

  // Sort the departments with natural number ordering for names
  return transformedData?.sort((a, b) => {
    if (a.progress !== b.progress) {
      return b.progress - a.progress; // Higher progress first
    }
    // Extract numbers from department names and compare them
    const aNum = parseInt(a.name.match(/\d+/)?.[0] || '0');
    const bNum = parseInt(b.name.match(/\d+/)?.[0] || '0');
    return aNum - bNum;
  });
}

export async function getDepartment(id: string) {
  const { data, error } = await supabase
    .from('departments')
    .select(`
      *,
      checklists (
        id,
        type,
        department_id,
        created_at,
        updated_at,
        checklist_items (
          id,
          description,
          completed,
          completed_by_username,
          completed_at,
          created_at,
          updated_at,
          position,
          is_header,
          template_id,
          category_id,
          checklist_categories!inner (
            id,
            name,
            created_at,
            updated_at
          ),
          checklist_item_notes (
            id,
            content,
            created_by_username,
            created_at
          )
        )
      ),
      department_issues (
        id,
        title,
        content,
        status,
        created_by_username,
        created_at,
        updated_at,
        task_id,
        task_description,
        checklist_type,
        category_name
      )
    `)
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching department:', error);
    throw error;
  }

  // Transform the data to match our expected structure
  if (data) {
    data.checklists = data.checklists.map((checklist: any) => ({
      ...checklist,
      checklist_items: checklist.checklist_items
        .map((item: any) => ({
          ...item,
          notes: Array.isArray(item.checklist_item_notes) ? item.checklist_item_notes : []
        }))
        // Sort by position to maintain order
        .sort((a: any, b: any) => (a.position || 0) - (b.position || 0))
    }));

    // Transform department issues
    data.issues = Array.isArray(data.department_issues) ? data.department_issues : [];
    delete data.department_issues;
  }

  return data;
}

// Checklist operations
export async function updateChecklistItem(id: string, completed: boolean, username: string) {
  const { data, error } = await supabase
    .rpc('toggle_checklist_item', {
      p_item_id: id,
      p_username: username
    });

  if (error) throw error;
  return data;
}

// Progress update operations
export async function updateDepartmentProgress(id: string, progress: number) {
  const { data, error } = await supabase
    .from('departments')
    .update({ progress })
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;

  // Update courthouse progress
  const department = data;
  const { data: departments } = await supabase
    .from('departments')
    .select('progress')
    .eq('courthouse_id', department.courthouse_id);

  if (departments) {
    const avgProgress = Math.round(
      departments.reduce((sum, dept) => sum + dept.progress, 0) / departments.length
    );

    await supabase
      .from('courthouses')
      .update({ progress: avgProgress })
      .eq('id', department.courthouse_id);

    // Update project progress
    const { data: courthouse } = await supabase
      .from('courthouses')
      .select('project_id, progress')
      .eq('id', department.courthouse_id)
      .single();

    if (courthouse) {
      const { data: courthouses } = await supabase
        .from('courthouses')
        .select('progress')
        .eq('project_id', courthouse.project_id);

      if (courthouses) {
        const projectProgress = Math.round(
          courthouses.reduce((sum, ch) => sum + ch.progress, 0) / courthouses.length
        );

        await supabase
          .from('projects')
          .update({ progress: projectProgress })
          .eq('id', courthouse.project_id);
      }
    }
  }

  return data;
}

// Note operations
export async function addChecklistItemNote(itemId: string, content: string, username: string) {
  const { data, error } = await supabase
    .rpc('add_checklist_item_note', {
      p_checklist_item_id: itemId,
      p_content: content,
      p_username: username
    });

  if (error) throw error;
  return data;
}

export async function getChecklistItemNotes(itemId: string) {
  const { data, error } = await supabase
    .from('checklist_item_notes')
    .select('*')
    .eq('checklist_item_id', itemId)
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data;
}

export async function deleteChecklistItemNote(noteId: string) {
  const { data, error } = await supabase
    .rpc('delete_checklist_item_note', {
      p_note_id: noteId
    });

  if (error) throw error;
  return data;
}

// Department note operations
export async function addDepartmentNote(departmentId: string, content: string, username: string) {
  const { data, error } = await supabase
    .rpc('add_department_note', {
      p_department_id: departmentId,
      p_content: content,
      p_username: username
    });

  if (error) throw error;
  return data;
}

export async function getDepartmentNotes(departmentId: string) {
  const { data, error } = await supabase
    .from('department_notes')
    .select('*')
    .eq('department_id', departmentId)
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data;
}

export async function deleteDepartmentNote(noteId: string) {
  const { data, error } = await supabase
    .rpc('delete_department_note', {
      p_note_id: noteId
    });

  if (error) throw error;
  return data;
}

export async function updateDepartmentNote(noteId: string, content: string) {
  const { data, error } = await supabase
    .rpc('update_department_note', {
      p_note_id: noteId,
      p_content: content
    });

  if (error) throw error;
  return data;
}

export async function updateChecklistItemNote(noteId: string, content: string) {
  const { data, error } = await supabase
    .rpc('update_checklist_item_note', {
      p_note_id: noteId,
      p_content: content
    });

  if (error) throw error;
  return data;
}

export async function addIssue(departmentId: string, issue: Omit<Issue, 'id' | 'created_at'>) {
  const { data, error } = await supabase
    .from('department_issues')
    .insert({
      department_id: departmentId,
      title: issue.title,
      content: issue.content,
      status: issue.status,
      created_by_username: issue.created_by_username,
      task_id: issue.task_id,
      task_description: issue.task_description,
      checklist_type: issue.checklist_type,
      category_name: issue.category_name
    })
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function updateIssue(issueId: string, issue: Partial<Omit<Issue, 'id' | 'created_at'>>) {
  const { data, error } = await supabase
    .from('department_issues')
    .update({
      title: issue.title,
      content: issue.content,
      status: issue.status,
      task_id: issue.task_id,
      task_description: issue.task_description,
      checklist_type: issue.checklist_type,
      category_name: issue.category_name
    })
    .eq('id', issueId)
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function deleteIssue(issueId: string) {
  const { error } = await supabase
    .from('department_issues')
    .delete()
    .eq('id', issueId);

  if (error) throw error;
}

// Company-Project relationship operations
export async function addCompanyProject(company: string, projectId: string) {
  const { data, error } = await supabase
    .from('company_projects')
    .insert({
      company,
      project_id: projectId
    })
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function removeCompanyProject(company: string, projectId: string) {
  const { error } = await supabase
    .from('company_projects')
    .delete()
    .eq('company', company)
    .eq('project_id', projectId);

  if (error) throw error;
}

export async function getCompanyProjects(company: string) {
  const { data, error } = await supabase
    .from('company_projects')
    .select('project_id')
    .eq('company', company);

  if (error) throw error;
  return data.map(item => item.project_id);
}

// Helper function to slugify strings
function slugify(str: string): string {
  return str
    .trim()
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-')        // Replace spaces with hyphens
    .replace(/-+/g, '-')         // Replace multiple hyphens with a single hyphen
    .replace(/^-+|-+$/g, '');    // Remove leading and trailing hyphens
}

export async function createDepartment(data: {
  courthouseId: string;
  name: string;
  referenceNumber: number;
  floor: number;
  selectedChecklistTypes: string[];
}) {
  // Generate a unique ID for the department using courthouseId and a slugified name
  const departmentSlug = slugify(data.name);
  const newDepartmentId = `${data.courthouseId}-${departmentSlug}`;

  // Note: This ID format relies on the combination of courthouse and department name slug being unique.
  // Consider adding collision handling if duplicate department name slugs within a courthouse are possible.

  // Insert the department basics first
  const { data: department, error: departmentError } = await supabase
    .from('departments')
    .insert({
      id: newDepartmentId,
      courthouse_id: data.courthouseId,
      name: data.name,
      reference_number: data.referenceNumber,
      floor: data.floor,
      status: 'on-track',
      progress: 0,
    })
    .select()
    .single();

  if (departmentError) {
    console.error("Error inserting department:", departmentError);
    throw new ErrorWithContext('Failed to create department record.', { cause: departmentError });
  }

  // Now, create the selected checklists linked to the new department
  if (data.selectedChecklistTypes && data.selectedChecklistTypes.length > 0) {
    console.log(`Creating checklists for department ${department.id}:`, data.selectedChecklistTypes);
    try {
      // Use Promise.all to create checklists concurrently
      await Promise.all(
        data.selectedChecklistTypes.map(type => createChecklist(department.id, type))
      );
      console.log(`Successfully created checklists for department ${department.id}`);
    } catch (checklistError) {
      // Log the error, but don't necessarily fail the whole department creation.
      // Depending on requirements, you might want to implement rollback logic here.
      console.error(`Error creating one or more checklists for department ${department.id}:`, checklistError);
      // Optionally re-throw or return a partial success indicator
      // throw new ErrorWithContext('Failed to create associated checklists.', { cause: checklistError });
    }
  }

  // Return the basic department data (without the old boolean flags)
  return {
    id: department.id,
    courthouse_id: department.courthouse_id,
    name: department.name,
    reference_number: department.reference_number,
    floor: department.floor,
    status: department.status,
    progress: department.progress,
    created_at: department.created_at,
    updated_at: department.updated_at,
    // Explicitly exclude has... flags if they exist on the raw return type
  };
}

// Helper function to create a checklist and populate it from the template
// Update type parameter to accept any string
async function createChecklist(departmentId: string, type: string) {
  console.log(`Creating checklist of type '${type}' for department ${departmentId}`);
  // 1. Find the template items for the given type
  const { data: templateItems, error: templateError } = await supabase
    .from('checklist_templates')
    .select('*')
    .eq('type', type);

  if (templateError) throw templateError;

  // 2. Create the checklist
  const { data: checklist, error: checklistError } = await supabase
    .from('checklists')
    .insert({
      id: crypto.randomUUID(),
      department_id: departmentId,
      type: type
    })
    .select()
    .single();

  if (checklistError) throw checklistError;

  // 3. Create checklist items based on the template
  if (templateItems && templateItems.length > 0) {
    const checklistItems = templateItems.map(template => ({
      id: crypto.randomUUID(),
      checklist_id: checklist.id,
      category_id: template.category_id,
      description: template.description,
      completed: false,
      position: template.position
    }));

    const { error: itemsError } = await supabase
      .from('checklist_items')
      .insert(checklistItems);

    if (itemsError) throw itemsError;
  }

  return checklist;
}